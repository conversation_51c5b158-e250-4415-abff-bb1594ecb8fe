{"Header": {"Time": "2025-09-16T01:51:06-07:00", "ReportName": "BalanceSheet", "ReportBasis": "Cash", "StartPeriod": "2025-07-01", "EndPeriod": "2025-07-31", "SummarizeColumnsBy": "Total", "Currency": "USD", "Option": [{"Name": "AccountingStandard", "Value": "GAAP"}, {"Name": "NoReportData", "Value": "false"}]}, "Columns": {"Column": [{"ColTitle": "", "ColType": "Account", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "account"}]}, {"ColTitle": "Total", "ColType": "Money", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "total"}]}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "ASSETS"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Current Assets"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Bank Accounts"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Money Market 4106", "id": "373"}, {"value": "111508.56"}], "type": "Data"}, {"ColData": [{"value": "Payables account 3609", "id": "225"}, {"value": "26799.05"}], "type": "Data"}, {"ColData": [{"value": "PNC Checking 6095 Stripe Account", "id": "188"}, {"value": "10507.20"}], "type": "Data"}, {"ColData": [{"value": "PNC Online Checking 3242", "id": "195"}, {"value": "200550.21"}], "type": "Data"}, {"ColData": [{"value": "Transfer", "id": "194"}, {"value": "0.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Bank Accounts"}, {"value": "349365.02"}]}, "type": "Section", "group": "BankAccounts"}, {"Header": {"ColData": [{"value": "Accounts Receivable"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Accounts Receivable", "id": "102"}, {"value": "0.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Accounts Receivable"}, {"value": "0.00"}]}, "type": "Section", "group": "AR"}]}, "Summary": {"ColData": [{"value": "Total Current Assets"}, {"value": "349365.02"}]}, "type": "Section", "group": "CurrentAssets"}, {"Header": {"ColData": [{"value": "Fixed Assets"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Accumulated Depreciation", "id": "80"}, {"value": "-313792.43"}], "type": "Data"}, {"ColData": [{"value": "Equipment", "id": "78"}, {"value": "9189.80"}], "type": "Data"}, {"ColData": [{"value": "Furniture", "id": "77"}, {"value": "129388.44"}], "type": "Data"}, {"ColData": [{"value": "Leasehold Improvements", "id": "79"}, {"value": "75909.85"}], "type": "Data"}, {"ColData": [{"value": "Office Buildout Expense", "id": "99"}, {"value": "762141.40"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Fixed Assets"}, {"value": "662837.06"}]}, "type": "Section", "group": "FixedAssets"}, {"Header": {"ColData": [{"value": "Other Assets"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Security Deposits 75th St.", "id": "370"}, {"value": "12795.30"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Other Assets"}, {"value": "12795.30"}]}, "type": "Section", "group": "OtherAssets"}]}, "Summary": {"ColData": [{"value": "TOTAL ASSETS"}, {"value": "1024997.38"}]}, "type": "Section", "group": "TotalAssets"}, {"Header": {"ColData": [{"value": "LIABILITIES AND EQUITY"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Liabilities"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Current Liabilities"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Credit Cards"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Chase credit card #4012", "id": "1150040000"}, {"value": "48328.47"}], "type": "Data"}, {"ColData": [{"value": "PNC credit card #0056", "id": "236"}, {"value": "-11.17"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Credit Cards"}, {"value": "48317.30"}]}, "type": "Section", "group": "CreditCards"}, {"Header": {"ColData": [{"value": "Other Current Liabilities"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "401K", "id": "267"}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "291"}, {"value": "576.92"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "292"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "356"}, {"value": "903.84"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "294"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "295"}, {"value": "903.84"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "296"}, {"value": "903.84"}], "type": "Data"}, {"ColData": [{"value": "401K - <PERSON><PERSON>", "id": "**********"}, {"value": "1236.84"}], "type": "Data"}, {"ColData": [{"value": "401K - Office Staff", "id": "289"}, {"value": "1351.98"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total 401K"}, {"value": "5877.26"}]}, "type": "Section"}, {"ColData": [{"value": "Bank Loan", "id": "98"}, {"value": "724471.97"}], "type": "Data"}, {"ColData": [{"value": "Direct Deposit Liabilities", "id": "**********"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Doctor Admin Income Payable", "id": "**********"}, {"value": "14587.50"}], "type": "Data"}, {"Header": {"ColData": [{"value": "Payroll Liabilities", "id": "7"}, {"value": "0.00"}]}, "Rows": {"Row": [{"ColData": [{"value": "BCBS Health", "id": "336"}, {"value": "262.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Liabilities", "id": "357"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Associate Dr<PERSON>", "id": "353"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Associates Dr. <PERSON>", "id": "365"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Dr<PERSON> <PERSON>", "id": "271"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Dr. <PERSON>", "id": "272"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON>", "id": "360"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON> <PERSON>", "id": "273"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON>", "id": "274"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Dr<PERSON>", "id": "275"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Office Staff", "id": "276"}, {"value": "43.75"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Payroll Liabilities"}, {"value": "305.75"}]}, "type": "Section"}, {"ColData": [{"value": "Profit Sharing Payable", "id": "82"}, {"value": "95958.66"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Other Current Liabilities"}, {"value": "841201.14"}]}, "type": "Section", "group": "OtherCurrentLiabilities"}]}, "Summary": {"ColData": [{"value": "Total Current Liabilities"}, {"value": "889518.44"}]}, "type": "Section", "group": "CurrentLiabilities"}]}, "Summary": {"ColData": [{"value": "Total Liabilities"}, {"value": "889518.44"}]}, "type": "Section", "group": "Liabilities"}, {"Header": {"ColData": [{"value": "Equity"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "BDS Distribution", "id": "241"}, {"value": "-65000.00"}], "type": "Data"}, {"ColData": [{"value": "Common Stock", "id": "86"}, {"value": "-32.15"}], "type": "Data"}, {"ColData": [{"value": "DG Distribution", "id": "367"}, {"value": "-20000.00"}], "type": "Data"}, {"ColData": [{"value": "JC Distribution", "id": "243"}, {"value": "-65000.00"}], "type": "Data"}, {"ColData": [{"value": "LG Distribution", "id": "240"}, {"value": "0.00"}], "type": "Data"}, {"ColData": [{"value": "MP Distribution", "id": "244"}, {"value": "-65000.00"}], "type": "Data"}, {"ColData": [{"value": "Opening Bal Equity", "id": "8"}, {"value": "75295.14"}], "type": "Data"}, {"ColData": [{"value": "Retained Earnings", "id": "87"}, {"value": "283020.05"}], "type": "Data"}, {"ColData": [{"value": "RS Distribution", "id": "242"}, {"value": "-65000.00"}], "type": "Data"}, {"ColData": [{"value": "Net Income"}, {"value": "57195.90"}], "type": "Data", "group": "NetIncome"}]}, "Summary": {"ColData": [{"value": "Total Equity"}, {"value": "135478.94"}]}, "type": "Section", "group": "Equity"}]}, "Summary": {"ColData": [{"value": "TOTAL LIABILITIES AND EQUITY"}, {"value": "1024997.38"}]}, "type": "Section", "group": "TotalLiabilitiesAndEquity"}]}}