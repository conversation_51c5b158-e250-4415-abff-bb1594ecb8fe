# Cashflow KPI Calculation System

This system calculates comprehensive cashflow metrics from cashflow data stored in the database and stores the results as JSO<PERSON> in the `kpi_snapshots` table.

## Overview

The cashflow KPI calculation system consists of:

1. **`calculate_cashflow.js`** - Calculates detailed cashflow metrics from stored cashflow data
2. **`query_cashflow.js`** - Queries and displays cashflow KPI results
3. **`kpi_snapshots` table** - Stores cashflow KPIs linked via `cashflow_report_id`

## Features

### Cashflow Analysis
- **Operating Cash Flow Analysis** - Detailed breakdown of operating activities
- **Cash Position Assessment** - Strong/Adequate/Concerning classification
- **Liquidity Analysis** - Cash runway and burn rate calculations
- **Period-over-Period Comparison** - Trend analysis across reporting periods
- **Cash Conversion Metrics** - Efficiency of converting income to cash

### Advanced Metrics
- **Cash Conversion Ratio** - Operating Cash Flow / Net Income
- **Cash Burn Rate** - Monthly cash consumption (if negative)
- **Cash Runway** - Months of cash remaining at current burn rate
- **Operating Margin** - Operating cash flow as % of net income
- **Cash Efficiency** - Net cash flow as % of ending cash

## Database Integration

### Cashflow Data Sources
- **`cashflow_totals`** - Aggregated totals by activity type
- **`cashflow_lines`** - Detailed line items with groupings
- **`cashflow_reports`** - Report metadata and periods

### KPI Storage
- **`kpi_snapshots.cashflow_kpi`** - JSONB column storing comprehensive metrics
- **`cashflow_report_id`** - Links to specific cashflow reports

## Cashflow KPI JSON Structure

```json
{
  "summary": {
    "operating_cash_flow": 103710.62,
    "investing_cash_flow": 0.00,
    "financing_cash_flow": 0.00,
    "net_cash_flow": 89434.62,
    "beginning_cash": 0.00,
    "ending_cash": 698730.04,
    "net_income": 30441.31,
    "calculation_date": "2025-09-05T08:30:24.123Z"
  },
  "metrics": {
    "cash_conversion_ratio": 3.41,
    "cash_burn_rate": 0,
    "cash_runway_months": null,
    "operating_margin": 340.7,
    "cash_efficiency": 12.8
  },
  "analysis": {
    "cash_position": "strong",
    "operating_trend": "positive",
    "liquidity_status": "excellent",
    "major_operating_items": [
      {
        "description": "Net cash provided by operating activities",
        "amount": 44717.31,
        "path": "OPERATING ACTIVITIES",
        "impact": "positive"
      }
    ]
  },
  "breakdown": {
    "operating_activities": {
      "total": 103710.62,
      "major_items": [...]
    },
    "investing_activities": {
      "total": 0.00,
      "items": []
    },
    "financing_activities": {
      "total": 0.00,
      "items": []
    }
  },
  "period_comparison": {
    "operating_change": 64778.97,
    "operating_change_percent": 166.4,
    "net_cash_change": 48693.50,
    "ending_cash_change": 89434.62
  }
}
```

## Usage

### Calculate Cashflow KPIs
```bash
# Calculate cashflow KPIs for the latest report
node calculate_cashflow.js

# Calculate cashflow KPIs for a specific report
node calculate_cashflow.js 123

# Calculate cashflow KPIs for all reports that don't have it yet
node calculate_cashflow.js all
```

### Query Cashflow Results
```bash
# Query cashflow KPIs for the latest report
node query_cashflow.js

# Query cashflow KPIs for a specific report
node query_cashflow.js 123
```

## Sample Output

### Calculation Output
```
Processing cashflow report 11: CashFlow (2025-07-01 to 2025-07-31)
   Found 1 cashflow totals and 29 cashflow lines
✅ Cashflow KPI calculated for report 11:
   Operating Cash Flow: $103,710.62
   Net Cash Flow: $89,434.62
   Ending Cash: $698,730.04
   Cash Position: strong
   Operating Trend: positive
   KPI Snapshot ID: 2
```

### Query Output
```
Cashflow Analysis Report
========================
Operating Cash Flow:  $103,710.62
Net Cash Flow:        $89,434.62
Ending Cash:          $698,730.04

Key Metrics:
------------
Cash Conversion Ratio: 3.41
Operating Margin:      340.7%
Cash Efficiency:       12.8%

Analysis:
---------
Cash Position:         STRONG
Operating Trend:       POSITIVE
Liquidity Status:      EXCELLENT

Period-over-Period Comparison:
------------------------------
Operating Cash Flow Change: +$64,778.97 (166.4%)
Net Cash Flow Change:       +$48,693.50
```

## Key Metrics Explained

### Cash Conversion Ratio
- **Formula**: Operating Cash Flow ÷ Net Income
- **Interpretation**: How efficiently the business converts earnings to cash
- **Good Range**: 1.0 - 1.5 (higher can indicate strong collections)

### Cash Position Classification
- **Strong**: Ending cash > $100,000
- **Adequate**: Ending cash > $50,000
- **Concerning**: Ending cash < $50,000

### Liquidity Status
- **Excellent**: Cash > 3x monthly operating cash flow
- **Good**: Cash > 1x monthly operating cash flow
- **Tight**: Cash < monthly operating cash flow

### Operating Trend
- **Positive**: Operating cash flow > 0
- **Negative**: Operating cash flow < 0

## Integration with Other KPIs

### Complete Financial Analysis Workflow
```bash
# 1. Ingest financial data
node ingest_pnl.js          # P&L data
node ingest_cashflow.js     # Cashflow data

# 2. Calculate all KPIs
node calculate_revenue.js   # Revenue KPIs
node calculate_ebitda.js    # EBITDA metrics
node calculate_cashflow.js  # Cashflow KPIs

# 3. View comprehensive analysis
node query_revenue.js       # Revenue analysis
node query_ebitda.js        # EBITDA analysis
node query_cashflow.js      # Cashflow analysis
```

### Cross-KPI Analysis
The cashflow KPIs complement other financial metrics:
- **Revenue vs Cash Flow** - Compare revenue generation to cash collection
- **EBITDA vs Operating Cash Flow** - Assess cash conversion efficiency
- **Expense Management** - Track cash outflows vs expense recognition

## Data Processing Logic

### Activity Classification
1. **Operating Activities** - Core business operations, adjustments to net income
2. **Investing Activities** - Capital expenditures, asset purchases/sales
3. **Financing Activities** - Debt, equity, dividend transactions

### Major Item Identification
- Filters items with absolute value > $1,000
- Ranks by impact magnitude
- Classifies as positive (cash inflow) or negative (cash outflow)

### Period Comparison
- Automatically compares with previous period if available
- Calculates absolute and percentage changes
- Tracks trends in key metrics

## Error Handling & Validation

### Data Validation
- Ensures numeric values for all calculations
- Validates cashflow totals exist before processing
- Handles null/undefined values gracefully

### Error Recovery
- Provides detailed error messages
- Supports batch processing with individual error handling
- Validates JSON structure before storage

## Performance Considerations

- **Efficient Queries** - Uses indexed lookups on report IDs
- **Minimal Memory Usage** - Processes one report at a time
- **Fast JSON Operations** - Optimized JSONB storage and retrieval
- **Batch Processing** - Supports processing multiple reports efficiently

## Business Intelligence Integration

The cashflow KPIs provide essential metrics for:
- **Cash Flow Forecasting** - Predict future cash positions
- **Working Capital Management** - Optimize cash conversion cycles
- **Investment Planning** - Assess cash availability for growth
- **Risk Assessment** - Monitor liquidity and cash burn rates
- **Performance Benchmarking** - Compare periods and set targets
