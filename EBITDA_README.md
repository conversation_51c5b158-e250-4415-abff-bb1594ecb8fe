# EBITDA Calculation System

This system calculates EBITDA (Earnings Before Interest, Taxes, Depreciation, and Amortization) from P&L data stored in the database.

## Overview

The EBITDA calculation system consists of several scripts that work together:

1. **`ingest_pnl.js`** - Fetches P&L data from QuickBooks API and stores it in the database
2. **`calculate_ebitda.js`** - Calculates EBITDA from stored P&L data
3. **`seed_ebitda_mappings.js`** - Seeds default account classification mappings
4. **`query_ebitda.js`** - Queries and displays EBITDA results

## Workflow

### Step 1: Ingest P&L Data
```bash
node ingest_pnl.js
```
This script:
- Fetches P&L data from QuickBooks API
- Stores raw data and PowerBI KPI JSON in `pnl_reports` table
- Stores line-by-line data in `pnl_lines` table
- Stores summary data in `pnl_summaries` table

### Step 2: Calculate EBITDA
```bash
# Calculate EBITDA for the latest report
node calculate_ebitda.js

# Calculate EBITDA for a specific report
node calculate_ebitda.js 123

# Calculate EBITDA for all reports that don't have it yet
node calculate_ebitda.js all
```

This script:
- Reads P&L data from the database (not from API)
- Classifies expense accounts into EBITDA categories
- Calculates EBIT and EBITDA metrics
- Stores results in `pnl_ebitda` table

### Step 3: Query EBITDA Results
```bash
# Query EBITDA for the latest report
node query_ebitda.js

# Query EBITDA for a specific report
node query_ebitda.js 123
```

### Optional: Seed Account Mappings
```bash
node seed_ebitda_mappings.js
```
Seeds default patterns for classifying accounts into Interest, Taxes, Depreciation, and Amortization categories.

## EBITDA Formula

**EBITDA = Net Income + Interest + Taxes + Depreciation + Amortization**

Where:
- **EBIT = Net Income + Interest + Taxes**
- **EBITDA = EBIT + Depreciation + Amortization**

## Database Tables

### `pnl_ebitda`
Stores calculated EBITDA metrics for each report:
- `report_id` - References `pnl_reports.id`
- `net_income` - Net income from P&L
- `interest_expense` - Total interest expenses
- `income_taxes` - Total tax expenses
- `depreciation` - Total depreciation expenses
- `amortization` - Total amortization expenses
- `ebit` - Calculated EBIT
- `ebitda` - Calculated EBITDA

### `ebitda_account_map`
Configurable account classification rules:
- `account_name_pattern` - Pattern to match account names (use % as wildcard)
- `ebitda_category` - Category: Interest, Taxes, Depreciation, or Amortization
- `priority` - Lower numbers have higher priority

## Account Classification

The system classifies expense accounts into EBITDA categories using:

1. **Database mappings** (from `ebitda_account_map` table)
2. **Hardcoded patterns** (fallback)

### Default Patterns

**Interest:**
- Interest Expense
- Interest Paid
- Loan Interest
- Financing Cost

**Taxes:**
- Income Tax
- Tax Expense
- Federal Tax
- State Tax

**Depreciation:**
- Depreciation
- Equipment Depreciation
- Building Depreciation

**Amortization:**
- Amortization
- Goodwill Amortization
- Intangible Amortization

## Sample Output

```
Processing report 123: Profit and Loss (2024-01-01 to 2024-12-31)
✅ EBITDA calculated for report 123:
   Net Income: $31,388.46
   Interest Expense: $3,880.41
   Income Taxes: $0.00
   Depreciation: $0.00
   Amortization: $0.00
   EBIT: $35,268.87
   EBITDA: $35,268.87
```

## Customization

### Adding Custom Account Mappings

You can add custom account classification rules by inserting into the `ebitda_account_map` table:

```sql
INSERT INTO ebitda_account_map (account_name_pattern, ebitda_category, priority)
VALUES ('%loan interest%', 'Interest', 5);
```

### Modifying Classification Logic

Edit the `classifyEBITDAAccount()` function in `calculate_ebitda.js` to add custom classification logic.

## Integration

The EBITDA system is designed to work independently of the P&L ingestion process:

1. **Separation of Concerns**: P&L ingestion and EBITDA calculation are separate processes
2. **Database-Driven**: EBITDA calculation reads from database, not API
3. **Flexible**: Can calculate EBITDA for any existing P&L report
4. **Batch Processing**: Can process multiple reports at once

## Error Handling

- If no P&L data exists, the script will inform you to run `ingest_pnl.js` first
- If EBITDA data doesn't exist, `query_ebitda.js` will suggest running `calculate_ebitda.js`
- All database operations use transactions and proper error handling

## Performance

- EBITDA calculation is fast since it reads from indexed database tables
- Can process multiple reports in batch mode
- Results are cached in the database for quick retrieval
