# P&L Categories System

This system adds expense categorization to P&L line items, enabling detailed financial analysis by category.

## Overview

The P&L categories system consists of:

1. **Database Schema** - Added `category` column to `pnl_lines` table
2. **Category Mapping** - `getCategoryForAccount()` function maps account names to categories
3. **Automatic Categorization** - Categories are assigned during P&L ingestion
4. **Analysis Tools** - Scripts to analyze expenses by category

## Categories

### 1. Operating Expenses
Core business operational costs:
- Cleaning - office
- Clinical Tool Licensure
- Equipment Maint. & Repair
- Legal & Accounting
- Medical Supplies
- Patient Notification Tools
- Office Expenses
- Practice Mgmt Software Lease
- Rent
- Telephone Office
- Utilities
- Vaccines
- Website Expense
- Workmans Comp Ins

### 2. Payroll Tax Expense
Employee compensation and related taxes:
- **Salary Expense**: Doctor and staff salaries
- **Payroll Expenses**: Payroll taxes and office staff payroll

### 3. Admin
Administrative expenses for doctors:
- Doctor Admin Income Expense (by doctor)

### 4. Dues, Subscriptions & License
Professional memberships and licenses:
- Dues, Subs & Lic (by doctor)

### 5. Other Expenses
Miscellaneous business expenses:
- Bank Charge
- Meals & Entertainment
- Profit Sharing
- Refunds
- Reimbursement
- Service fee
- Interest Expense

### 6. Health Insurance Expense (Medical)
Medical insurance costs:
- Health Insurance (by individual)

## Database Schema

### Updated `pnl_lines` Table
```sql
ALTER TABLE pnl_lines ADD COLUMN category VARCHAR(255);
```

The category column stores the expense category name for each P&L line item.

## Usage

### 1. Automatic Categorization (New Data)
Categories are automatically assigned during P&L ingestion:

```bash
node ingest_pnl.js  # New P&L data will include categories
```

### 2. Update Existing Data
For existing P&L data without categories:

```bash
node update_pnl_categories.js  # Adds categories to existing data
```

### 3. Category Analysis
Analyze expenses by category:

```bash
# Analyze latest P&L report
node query_pnl_categories.js

# Analyze specific report
node query_pnl_categories.js 123
```

## Sample Analysis Output

```
P&L Category Analysis Report
============================
Report ID: 6
Period: 2025-06-01 to 2025-06-30

Category Summary:
-----------------
Payroll Tax Expense:
  Total Amount: $134,380.68
  Line Items: 14
  Unique Accounts: 14

Operating Expenses:
  Total Amount: $99,295.20
  Line Items: 14
  Unique Accounts: 14

Other Expenses:
  Total Amount: $21,076.75
  Line Items: 7
  Unique Accounts: 7

📈 CATEGORIZATION STATISTICS
============================
Total P&L Lines: 65
Categorized Lines: 43 (66.2%)
Categories Found: 5
```

## Key Features

### Comprehensive Analysis
- **Category Totals** - Sum of expenses by category
- **Account Breakdown** - Detailed view within each category
- **Uncategorized Items** - Identifies accounts needing categorization
- **Statistics** - Categorization coverage and insights

### Business Intelligence
- **Expense Trends** - Track category spending over time
- **Budget Variance** - Compare actual vs budgeted by category
- **Cost Control** - Identify high-spending categories
- **Operational Insights** - Understand expense composition

### Data Quality
- **Coverage Tracking** - Shows categorization percentage
- **Missing Categories** - Highlights uncategorized accounts
- **Validation** - Ensures data integrity

## Integration with KPI System

### Revenue KPIs
Categories complement revenue analysis by providing expense breakdown:
- **Gross Margin Analysis** - Revenue minus categorized expenses
- **Category Profitability** - Revenue efficiency by expense type

### EBITDA Calculations
Categories enhance EBITDA analysis:
- **Operating vs Non-Operating** - Separate operational expenses
- **Interest Classification** - Identify interest expenses automatically

### Cashflow Analysis
Categories provide context for cash flow patterns:
- **Operating Cash Flow** - Link to operational expense categories
- **Cash Burn Analysis** - Understand cash usage by category

## Extending Categories

### Adding New Categories
1. Update `getCategoryForAccount()` function in `ingest_pnl.js`
2. Add new account names to appropriate category arrays
3. Run `update_pnl_categories.js` to update existing data

### Example: Adding Insurance Category
```javascript
// In getCategoryForAccount() function
if (["Insurance-Malpractice - DG", "Insurance-Malpractice - SS", 
     "Insurance-Malpractice - MP", "Insurance-Malpractice - BDS",
     "Insurance-Malpractice - RS", "Insurance-Malpractice - JC",
     "Insurance-Malpractice - LG"].includes(accountName)) {
  return "Insurance Expense";
}
```

## Performance Considerations

### Database Indexing
Consider adding index for category queries:
```sql
CREATE INDEX idx_pnl_lines_category ON pnl_lines(category);
CREATE INDEX idx_pnl_lines_report_category ON pnl_lines(report_id, category);
```

### Query Optimization
- Category analysis uses aggregated queries for performance
- Batch updates for existing data categorization
- Efficient account name matching using arrays

## Business Value

### Financial Management
- **Expense Control** - Monitor spending by category
- **Budget Planning** - Historical category data for budgeting
- **Variance Analysis** - Compare actual vs planned by category
- **Cost Optimization** - Identify high-cost categories for review

### Operational Insights
- **Resource Allocation** - Understand where money is spent
- **Efficiency Metrics** - Category spending per revenue dollar
- **Trend Analysis** - Track category changes over time
- **Benchmarking** - Compare category ratios to industry standards

### Compliance & Reporting
- **Tax Preparation** - Organized expense categories
- **Financial Statements** - Detailed expense breakdown
- **Audit Support** - Clear expense classification
- **Management Reporting** - Category-based dashboards

## Future Enhancements

### Planned Features
- **Subcategories** - More granular expense classification
- **Budget Integration** - Category-based budget vs actual
- **Alerts** - Threshold-based category spending alerts
- **Forecasting** - Predictive category spending models

### Advanced Analytics
- **Category Ratios** - Expense ratios by category
- **Seasonal Analysis** - Category spending patterns by period
- **Efficiency Metrics** - Category cost per revenue metrics
- **Benchmarking** - Industry comparison by category

## Maintenance

### Regular Tasks
```bash
# Monthly category analysis
node query_pnl_categories.js

# Update categories for new accounts
node update_pnl_categories.js

# Review uncategorized items
node query_pnl_categories.js | grep "UNCATEGORIZED"
```

### Data Quality Checks
- Monitor categorization percentage (target: >90%)
- Review new uncategorized accounts monthly
- Validate category totals against P&L summaries
- Ensure consistent category naming

The P&L categories system provides essential expense classification capabilities that enhance financial analysis, budgeting, and operational decision-making across the entire financial management workflow.
