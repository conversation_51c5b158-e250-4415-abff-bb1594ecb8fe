# QuickBooks Data System - Complete Setup and Usage Guide

This comprehensive guide provides step-by-step instructions for setting up and using the QuickBooks data ingestion and KPI calculation system.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Setup](#database-setup)
4. [Data Ingestion](#data-ingestion)
5. [KPI Calculations](#kpi-calculations)
6. [Analysis and Reporting](#analysis-and-reporting)
7. [Maintenance Scripts](#maintenance-scripts)
8. [Complete Workflow Examples](#complete-workflow-examples)
9. [Troubleshooting](#troubleshooting)
10. [Advanced Usage](#advanced-usage)

## Prerequisites

### Required Software

- **Node.js** (v18 or higher)
- **PostgreSQL** (v12 or higher)
- **QuickBooks Online API Access**

### Required Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# PostgreSQL Database Configuration
PGHOST=your_postgres_host
PGPORT=5432
PGDATABASE=your_database_name
PGUSER=your_postgres_user
PGPASSWORD=your_postgres_password

# QuickBooks API Configuration
QBO_BASE=https://sandbox-quickbooks.api.intuit.com
QBO_COMPANY_ID=your_company_id
QBO_ACCESS_TOKEN=your_access_token

# Date Ranges for Data Ingestion
PNL_START=2025-01-01
PNL_END=2025-12-31
CF_START=2025-01-01
CF_END=2025-12-31
```

## Environment Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Verify Environment

```bash
# Check Node.js version
node --version

# Check PostgreSQL connection
psql -h $PGHOST -U $PGUSER -d $PGDATABASE -c "SELECT version();"
```

## Database Setup

### Phase 1: Run Database Migrations

#### Option A: Automated Migration Runner (Recommended)

```bash
# Run all migrations with tracking and verification
node run_migrations.js

# Verify migrations only (no changes)
node run_migrations.js --verify-only

# View migration options
node run_migrations.js --help
```

#### Option B: Manual SQL Migration

```bash
# Navigate to migrations directory
cd migrations

# Run all migrations at once
psql -d $PGDATABASE -f run_all_migrations.sql

# OR run individual migrations in order
psql -d $PGDATABASE -f 001_create_accounts_table.sql
psql -d $PGDATABASE -f 002_create_pnl_reports_table.sql
psql -d $PGDATABASE -f 003_create_pnl_lines_table.sql
psql -d $PGDATABASE -f 004_create_pnl_summaries_table.sql
psql -d $PGDATABASE -f 005_create_pnl_ebitda_table.sql
psql -d $PGDATABASE -f 006_create_cashflow_reports_table.sql
psql -d $PGDATABASE -f 007_create_cashflow_lines_table.sql
psql -d $PGDATABASE -f 008_create_cashflow_totals_table.sql
psql -d $PGDATABASE -f 009_create_kpi_snapshots_table.sql
psql -d $PGDATABASE -f 010_create_ebitda_account_map_table.sql
```

### Phase 2: Verify Database Schema

```bash
# Comprehensive database verification
node verify_database.js

# Detailed verification with additional info
node verify_database.js --detailed

# Quick schema verification using SQL
psql -d $PGDATABASE -f migrations/verify_schema.sql
```

### Phase 3: Seed Reference Data (Optional)

```bash
# Seed default EBITDA account mappings
node seed_ebitda_mappings.js
```

## Data Ingestion

### Phase 1: Ingest Chart of Accounts

```bash
# Fetch and store QuickBooks chart of accounts
node ingest_accounts.js

# Verify accounts data
node check_accounts_table.js

# Fix accounts table if needed
node fix_accounts_table.js
```

### Phase 2: Ingest Financial Reports

#### P&L Data Ingestion

```bash
# Ingest P&L data for date range specified in .env
node ingest_pnl.js

# Verify P&L data ingestion
psql -d $PGDATABASE -c "SELECT COUNT(*) as pnl_reports FROM pnl_reports;"
psql -d $PGDATABASE -c "SELECT COUNT(*) as pnl_lines FROM pnl_lines;"
```

#### Cashflow Data Ingestion

```bash
# Ingest cashflow data for date range specified in .env
node ingest_cashflow.js 2025-01-01 2025-12-31

# OR use environment variables
node ingest_cashflow.js

# Verify cashflow data ingestion
psql -d $PGDATABASE -c "SELECT COUNT(*) as cashflow_reports FROM cashflow_reports;"
psql -d $PGDATABASE -c "SELECT COUNT(*) as cashflow_lines FROM cashflow_lines;"
```

## KPI Calculations

### Phase 1: Calculate Individual KPIs

#### Revenue KPI Calculation

```bash
# Calculate revenue KPI for latest P&L report
node calculate_revenue.js

# Calculate revenue KPI for specific report
node calculate_revenue.js 123

# Calculate revenue KPI for all reports missing it
node calculate_revenue.js all
```

#### EBITDA Calculation

```bash
# Calculate EBITDA for latest P&L report
node calculate_ebitda.js

# Calculate EBITDA for specific report
node calculate_ebitda.js 123

# Calculate EBITDA for all reports missing it
node calculate_ebitda.js all
```

#### Cashflow KPI Calculation

```bash
# Calculate cashflow KPI for latest cashflow report
node calculate_cashflow.js

# Calculate cashflow KPI for specific report
node calculate_cashflow.js 123

# Calculate cashflow KPI for all reports missing it
node calculate_cashflow.js all
```

### Phase 2: Batch KPI Calculation

```bash
# Calculate all KPIs for all reports (comprehensive)
node calculate_revenue.js all
node calculate_ebitda.js all
node calculate_cashflow.js all
```

## Analysis and Reporting

### Individual KPI Analysis

#### Revenue Analysis

```bash
# Analyze latest P&L report revenue
node query_revenue.js

# Analyze specific P&L report revenue
node query_revenue.js 123
```

#### EBITDA Analysis

```bash
# Analyze latest P&L report EBITDA
node query_ebitda.js

# Analyze specific P&L report EBITDA
node query_ebitda.js 123
```

#### Cashflow Analysis

```bash
# Analyze latest cashflow report
node query_cashflow.js

# Analyze specific cashflow report
node query_cashflow.js 123
```

#### P&L Categories Analysis

```bash
# Analyze P&L categories for latest report
node query_pnl_categories.js

# Analyze P&L categories for specific report
node query_pnl_categories.js 123
```

### Comprehensive Analysis

```bash
# Complete financial dashboard (all KPIs)
node query_all_kpis.js

# Comprehensive analysis with specific reports
node query_all_kpis.js 123 456  # pnl_report_id cashflow_report_id
```

## Maintenance Scripts

### P&L Categories Management

```bash
# Update existing P&L data with categories
node update_pnl_categories.js

# Analyze category coverage
node query_pnl_categories.js | grep "CATEGORIZATION STATISTICS"
```

### Database Maintenance

```bash
# Check database health
node verify_database.js

# Check accounts table structure
node check_accounts_table.js

# Fix accounts table if corrupted
node fix_accounts_table.js
```

### Data Verification

```bash
# Verify all table counts
psql -d $PGDATABASE -c "
SELECT
  'accounts' as table_name, COUNT(*) as count FROM accounts
UNION ALL
SELECT 'pnl_reports', COUNT(*) FROM pnl_reports
UNION ALL
SELECT 'pnl_lines', COUNT(*) FROM pnl_lines
UNION ALL
SELECT 'cashflow_reports', COUNT(*) FROM cashflow_reports
UNION ALL
SELECT 'cashflow_lines', COUNT(*) FROM cashflow_lines
UNION ALL
SELECT 'kpi_snapshots', COUNT(*) FROM kpi_snapshots;
"
```

## Complete Workflow Examples

### Initial Setup (First Time)

```bash
# 1. Setup database
node run_migrations.js
node verify_database.js

# 2. Seed reference data
node seed_ebitda_mappings.js

# 3. Ingest base data
node ingest_accounts.js
node ingest_pnl.js
node ingest_cashflow.js

# 4. Calculate all KPIs
node calculate_revenue.js all
node calculate_ebitda.js all
node calculate_cashflow.js all

# 5. Update P&L categories
node update_pnl_categories.js

# 6. Generate comprehensive report
node query_all_kpis.js
```

### Monthly Data Update

```bash
# 1. Ingest new data
node ingest_pnl.js
node ingest_cashflow.js

# 2. Calculate KPIs for new data
node calculate_revenue.js all
node calculate_ebitda.js all
node calculate_cashflow.js all

# 3. Generate reports
node query_all_kpis.js
node query_pnl_categories.js
```

### Quarterly Analysis

```bash
# 1. Verify data integrity
node verify_database.js

# 2. Update categories for new accounts
node update_pnl_categories.js

# 3. Generate comprehensive analysis
node query_all_kpis.js
node query_revenue.js
node query_ebitda.js
node query_cashflow.js
node query_pnl_categories.js
```

## Troubleshooting

### Common Issues and Solutions

#### Database Connection Issues

```bash
# Test database connection
psql -h $PGHOST -U $PGUSER -d $PGDATABASE -c "SELECT 1;"

# Check environment variables
echo $PGHOST $PGPORT $PGDATABASE $PGUSER
```

#### Migration Issues

```bash
# Check migration status
psql -d $PGDATABASE -c "SELECT * FROM migration_history ORDER BY executed_at;"

# Reset migrations (CAUTION: This will drop all data)
psql -d $PGDATABASE -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
node run_migrations.js
```

#### API Connection Issues

```bash
# Verify QuickBooks API credentials
echo $QBO_BASE $QBO_COMPANY_ID
echo "Access Token: ${QBO_ACCESS_TOKEN:0:20}..." # Show first 20 chars only
```

#### Data Ingestion Issues

```bash
# Check for failed ingestions
psql -d $PGDATABASE -c "
SELECT
  'P&L Reports' as type, COUNT(*) as count, MAX(created_at) as latest
FROM pnl_reports
UNION ALL
SELECT
  'Cashflow Reports', COUNT(*), MAX(created_at)
FROM cashflow_reports;
"
```

#### KPI Calculation Issues

```bash
# Check KPI calculation status
psql -d $PGDATABASE -c "
SELECT
  COUNT(CASE WHEN total_revenue_kpi IS NOT NULL THEN 1 END) as revenue_kpis,
  COUNT(CASE WHEN ebitda_kpi IS NOT NULL THEN 1 END) as ebitda_kpis,
  COUNT(CASE WHEN cashflow_kpi IS NOT NULL THEN 1 END) as cashflow_kpis,
  COUNT(*) as total_snapshots
FROM kpi_snapshots;
"
```

### Error Recovery

#### Corrupted Accounts Table

```bash
# Fix accounts table structure
node fix_accounts_table.js

# Re-ingest accounts data
node ingest_accounts.js
```

#### Missing KPI Data

```bash
# Recalculate all missing KPIs
node calculate_revenue.js all
node calculate_ebitda.js all
node calculate_cashflow.js all
```

#### Category Data Issues

```bash
# Update all P&L categories
node update_pnl_categories.js

# Verify category coverage
node query_pnl_categories.js | grep "Categorized Lines"
```

## Advanced Usage

### Custom Date Ranges

```bash
# Ingest specific date ranges
CF_START=2025-06-01 CF_END=2025-06-30 node ingest_cashflow.js
PNL_START=2025-06-01 PNL_END=2025-06-30 node ingest_pnl.js
```

### Batch Processing

```bash
# Process multiple months of data
for month in {01..12}; do
  echo "Processing 2025-${month}"
  CF_START="2025-${month}-01" CF_END="2025-${month}-28" node ingest_cashflow.js
done
```

### Database Queries for Custom Analysis

```sql
-- Top revenue-generating doctors
SELECT
  jsonb_path_query(total_revenue_kpi, '$.metrics.revenue_by_doctor[*]') as doctor_revenue
FROM kpi_snapshots
WHERE total_revenue_kpi IS NOT NULL
ORDER BY pnl_report_id DESC
LIMIT 1;

-- EBITDA trends over time
SELECT
  pr.start_date,
  pr.end_date,
  (ks.ebitda_kpi->>'ebitda')::numeric as ebitda
FROM pnl_reports pr
JOIN kpi_snapshots ks ON pr.id = ks.pnl_report_id
WHERE ks.ebitda_kpi IS NOT NULL
ORDER BY pr.start_date;

-- Cashflow analysis by category
SELECT
  jsonb_path_query(cashflow_kpi, '$.summary') as cashflow_summary
FROM kpi_snapshots
WHERE cashflow_kpi IS NOT NULL
ORDER BY cashflow_report_id DESC
LIMIT 1;
```

### Performance Optimization

```sql
-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_pnl_lines_report_id ON pnl_lines(report_id);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_category ON pnl_lines(category);
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_report_id ON cashflow_lines(report_id);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_pnl_report ON kpi_snapshots(pnl_report_id);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_cashflow_report ON kpi_snapshots(cashflow_report_id);
```

## System Architecture

### Database Tables

- **`accounts`** - QuickBooks chart of accounts
- **`pnl_reports`** - P&L report metadata and raw data
- **`pnl_lines`** - Detailed P&L line items with categories
- **`pnl_summaries`** - P&L section totals
- **`pnl_ebitda`** - EBITDA calculations (legacy)
- **`cashflow_reports`** - Cashflow report metadata and raw data
- **`cashflow_lines`** - Detailed cashflow line items
- **`cashflow_totals`** - Cashflow summary totals by category
- **`kpi_snapshots`** - Centralized KPI storage in JSONB format
- **`ebitda_account_map`** - Account classification mappings for EBITDA
- **`migration_history`** - Migration tracking

### Data Flow

1. **Ingestion**: QuickBooks API → Raw tables (`*_reports`, `*_lines`)
2. **Calculation**: Raw data → KPI calculations → `kpi_snapshots`
3. **Analysis**: `kpi_snapshots` → Query scripts → Reports

### KPI Structure

All KPIs are stored in JSONB format in the `kpi_snapshots` table:

- **`total_revenue_kpi`** - Revenue analysis and doctor performance
- **`ebitda_kpi`** - EBITDA calculations and margins
- **`cashflow_kpi`** - Cashflow analysis by category

## Support and Maintenance

### Regular Maintenance Tasks

```bash
# Weekly
node verify_database.js
node query_all_kpis.js

# Monthly
node ingest_pnl.js
node ingest_cashflow.js
node calculate_revenue.js all
node calculate_ebitda.js all
node calculate_cashflow.js all
node update_pnl_categories.js

# Quarterly
node seed_ebitda_mappings.js  # Update if new account patterns found
```

### Monitoring

```bash
# Check system health
node verify_database.js

# Monitor data freshness
psql -d $PGDATABASE -c "
SELECT
  'Latest P&L' as type,
  MAX(end_date) as latest_date,
  COUNT(*) as total_reports
FROM pnl_reports
UNION ALL
SELECT
  'Latest Cashflow',
  MAX(end_date),
  COUNT(*)
FROM cashflow_reports;
"
```

### Script Reference

#### Migration Scripts

- **`run_migrations.js`** - Automated migration runner with tracking
- **`verify_database.js`** - Comprehensive database schema verification
- **`migrations/run_all_migrations.sql`** - SQL-based migration runner
- **`migrations/verify_schema.sql`** - SQL-based schema verification

#### Data Ingestion Scripts

- **`ingest_accounts.js`** - Fetch and store QuickBooks chart of accounts
- **`ingest_pnl.js`** - Ingest P&L reports with automatic categorization
- **`ingest_cashflow.js`** - Ingest cashflow reports by date range

#### KPI Calculation Scripts

- **`calculate_revenue.js`** - Calculate revenue KPIs and doctor performance
- **`calculate_ebitda.js`** - Calculate EBITDA metrics and margins
- **`calculate_cashflow.js`** - Calculate cashflow KPIs by category

#### Analysis and Query Scripts

- **`query_revenue.js`** - Revenue analysis and reporting
- **`query_ebitda.js`** - EBITDA analysis and reporting
- **`query_cashflow.js`** - Cashflow analysis and reporting
- **`query_pnl_categories.js`** - P&L expense category analysis
- **`query_all_kpis.js`** - Comprehensive financial dashboard

#### Maintenance Scripts

- **`update_pnl_categories.js`** - Update existing P&L data with categories
- **`seed_ebitda_mappings.js`** - Seed default EBITDA account mappings
- **`check_accounts_table.js`** - Verify accounts table structure
- **`fix_accounts_table.js`** - Fix corrupted accounts table

### Environment Variables Reference

```bash
# Database Configuration
PGHOST=localhost                    # PostgreSQL host
PGPORT=5432                        # PostgreSQL port
PGDATABASE=qbo_data                # Database name
PGUSER=postgres                    # Database user
PGPASSWORD=your_password           # Database password

# QuickBooks API Configuration
QBO_BASE=https://sandbox-quickbooks.api.intuit.com  # API base URL
QBO_COMPANY_ID=123456789012345678901234567890      # Company ID
QBO_ACCESS_TOKEN=your_access_token                  # OAuth access token

# Date Range Configuration
PNL_START=2025-01-01              # P&L report start date
PNL_END=2025-12-31                # P&L report end date
CF_START=2025-01-01               # Cashflow report start date
CF_END=2025-12-31                 # Cashflow report end date
```

This comprehensive guide provides everything needed to set up, operate, and maintain the QuickBooks data system. Follow the phases in order for initial setup, then use the maintenance scripts for ongoing operations.
