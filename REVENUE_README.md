# Revenue Calculation System

This system calculates comprehensive revenue metrics from P&L data stored in the database and stores the results as JSO<PERSON> in the `kpi_snapshots` table.

## Overview

The revenue calculation system consists of:

1. **`calculate_revenue.js`** - Calculates detailed revenue metrics from stored P&L data
2. **`query_revenue.js`** - Queries and displays revenue KPI results
3. **`kpi_snapshots` table** - Stores all KPI data including revenue metrics

## Features

### Revenue Analysis
- **Total Revenue Calculation** - Aggregates all income sources
- **Practice Income Breakdown** - Revenue by individual doctors and office staff
- **Other Income Tracking** - Non-practice income sources
- **Doctor Performance Analysis** - Individual and comparative metrics
- **Revenue Composition** - Percentage breakdown of income sources

### Data Storage
- **JSON Format** - Flexible schema stored in `kpi_snapshots.total_revenue_kpi`
- **Database Integration** - Links to P&L reports via `pnl_report_id`
- **Historical Tracking** - Maintains calculation timestamps

## Database Schema

### `kpi_snapshots` Table
```sql
CREATE TABLE kpi_snapshots (
  id BIGSERIAL PRIMARY KEY,
  pnl_report_id BIGINT REFERENCES pnl_reports(id) ON DELETE SET NULL,
  cashflow_report_id BIGINT REFERENCES cashflow_reports(id) ON DELETE SET NULL,
  
  -- KPI payloads stored as JSONB for flexibility
  total_expense_kpi JSONB,
  total_revenue_kpi JSONB,
  ebitda_kpi JSONB,
  cashflow_kpi JSONB,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## Revenue KPI JSON Structure

```json
{
  "summary": {
    "total_revenue": 297414.11,
    "practice_income": 298927.49,
    "other_income": -1513.38,
    "number_of_doctors": 7,
    "calculation_date": "2025-09-05T08:19:46.123Z"
  },
  "breakdown": {
    "practice_income": {
      "doctors": {
        "Canner": 55062.67,
        "Ghosh": 54705.53,
        "Pierce": 49971.41,
        "Scherer": 49858.75,
        "Shah": 44058.00,
        "Shaikh": 38311.64,
        "George": 2533.89
      },
      "office_staff": 4425.60,
      "total": 298927.49
    },
    "other_income": {
      "items": {
        "Interest Income": 218.65,
        "Credit Card Rewards": 11.17,
        "Uncategorized Income": 0.00,
        "Unapplied Cash Payment Income": -1743.20
      },
      "total": -1513.38
    },
    "total_revenue": 297414.11
  },
  "metrics": {
    "revenue_by_doctor": [
      {
        "doctor": "Canner",
        "amount": 55062.67,
        "percentage": 18.5
      }
    ],
    "practice_vs_other": {
      "practice_income": 298927.49,
      "practice_percentage": 100.5,
      "other_income": -1513.38,
      "other_percentage": -0.5
    },
    "top_revenue_sources": [
      {
        "source": "Dr. Canner",
        "amount": 55062.67,
        "category": "Practice Income"
      }
    ]
  }
}
```

## Usage

### Calculate Revenue KPI
```bash
# Calculate revenue for the latest report
node calculate_revenue.js

# Calculate revenue for a specific report
node calculate_revenue.js 123

# Calculate revenue for all reports that don't have it yet
node calculate_revenue.js all
```

### Query Revenue Results
```bash
# Query revenue for the latest report
node query_revenue.js

# Query revenue for a specific report
node query_revenue.js 123
```

## Sample Output

### Calculation Output
```
Processing report 6: ProfitAndLoss (2025-06-01 to 2025-06-30)
   Found 12 income lines
✅ Revenue KPI calculated for report 6:
   Total Revenue: $297,414.11
   Practice Income: $298,927.49 (100.5%)
   Other Income: $-1,513.38 (-0.5%)
   Number of Doctors: 7
   KPI Snapshot ID: 1
   Top Revenue Sources:
     1. Dr. Canner: $55,062.67
     2. Dr. Ghosh: $54,705.53
     3. Dr. Pierce: $49,971.41
     4. Dr. Scherer: $49,858.75
     5. Dr. Shah: $44,058.00
```

### Query Output
```
Revenue Analysis Report
=======================
Total Revenue:     $297,414.11
Practice Income:   $298,927.49 (100.5%)
Other Income:      $-1,513.38 (-0.5%)
Number of Doctors: 7

Revenue by Doctor:
------------------
1. Dr. Canner: $55,062.67 (18.5%)
2. Dr. Ghosh: $54,705.53 (18.4%)
3. Dr. Pierce: $49,971.41 (16.8%)
...

Additional Metrics:
-------------------
Average Revenue per Doctor: $42,703.93

Doctor Performance vs Average:
  Dr. Canner: +28.9% vs average
  Dr. Ghosh: +28.1% vs average
  Dr. Pierce: +17.0% vs average
  ...
```

## Key Metrics Provided

### Summary Metrics
- **Total Revenue** - Sum of all income sources
- **Practice Income** - Revenue from medical practice
- **Other Income** - Non-practice revenue (interest, rewards, etc.)
- **Number of Doctors** - Count of revenue-generating doctors

### Doctor Analysis
- **Individual Revenue** - Revenue per doctor
- **Percentage Contribution** - Each doctor's share of total revenue
- **Performance vs Average** - How each doctor compares to the average
- **Ranking** - Doctors ranked by revenue generation

### Revenue Composition
- **Practice vs Other** - Breakdown of revenue sources
- **Top Revenue Sources** - Highest contributing income sources
- **Detailed Breakdown** - Complete categorization of all income

## Integration with Other Systems

### EBITDA Integration
The revenue KPI works alongside the EBITDA calculation system:
- Revenue data feeds into EBITDA margin calculations
- Both systems use the same P&L report data
- KPI snapshots can store both revenue and EBITDA metrics

### Workflow Integration
```bash
# Complete P&L analysis workflow
node ingest_pnl.js          # 1. Ingest P&L data from API
node calculate_revenue.js    # 2. Calculate revenue KPIs
node calculate_ebitda.js     # 3. Calculate EBITDA metrics
node query_revenue.js        # 4. View revenue analysis
node query_ebitda.js         # 5. View EBITDA analysis
```

## Data Processing Logic

### Income Classification
1. **Doctor Revenue** - Identified by path containing "Dr. [Name] - Income"
2. **Office Staff** - Identified by path containing "Office - Staff Income"
3. **Other Income** - All remaining income not classified as practice income

### Path-Based Parsing
The system uses the hierarchical path from P&L data rather than account names:
- `Income > Practice Income > Dr. Canner - Income` → Dr. Canner revenue
- `Income > Practice Income > Office - Staff Income` → Office staff revenue
- `Income` (direct) → Other income

### Error Handling
- Validates numeric data before processing
- Handles null/undefined values gracefully
- Provides detailed logging for troubleshooting
- Supports batch processing with error recovery

## Performance
- Fast database queries using indexed lookups
- Efficient JSON storage and retrieval
- Minimal memory footprint
- Supports concurrent processing of multiple reports
