#!/usr/bin/env node
/**
 * Calculate cashflow KPIs from cashflow data stored in the database.
 * This script reads cashflow data and calculates comprehensive cashflow metrics.
 * 
 * Usage:
 *   node calculate_cashflow.js [report_id]
 *   
 * If no report_id is provided, calculates cashflow KPIs for the latest report.
 * If report_id is "all", calculates cashflow KPIs for all reports that don't have it yet.
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter or "all"

async function ensureKPISchema() {
  // KPI snapshots table (should already exist from revenue script)
  await pg.query(`
    create table if not exists kpi_snapshots (
      id bigserial primary key,
      pnl_report_id bigint references pnl_reports(id) on delete set null,
      cashflow_report_id bigint references cashflow_reports(id) on delete set null,
      
      -- KPI payloads stored as JSONB for flexibility
      total_expense_kpi jsonb,
      total_revenue_kpi jsonb,
      ebitda_kpi jsonb,
      cashflow_kpi jsonb,
      
      -- timestamp for when this KPI snapshot was created
      created_at timestamptz not null default now()
    );
  `);
  
  // Create index for faster lookups
  await pg.query(`
    create index if not exists idx_kpi_snapshots_cashflow_report_id 
    on kpi_snapshots(cashflow_report_id);
  `);
}

function parseAmount(value) {
  if (value === null || value === undefined) return 0;
  const num = Number(value);
  return Number.isFinite(num) ? num : 0;
}

async function calculateCashflowForReport(reportId) {
  // Get cashflow totals
  const { rows: totalsRows } = await pg.query(`
    SELECT operating, investing, financing, net_cash_flow, beginning_cash, ending_cash
    FROM cashflow_totals 
    WHERE report_id = $1
  `, [reportId]);
  
  if (!totalsRows.length) {
    throw new Error(`No cashflow totals found for report ${reportId}`);
  }
  
  const totals = totalsRows[0];

  // Get detailed cashflow lines for analysis
  const { rows: linesRows } = await pg.query(`
    SELECT path, label, "group", amount 
    FROM cashflow_lines 
    WHERE report_id = $1 
    ORDER BY "group", ABS(amount) DESC
  `, [reportId]);

  // Categorize and analyze cashflow components
  const operatingDetails = linesRows.filter(line => line.group === 'Operating');
  const investingDetails = linesRows.filter(line => line.group === 'Investing');
  const financingDetails = linesRows.filter(line => line.group === 'Financing');

  // Calculate key metrics
  const operating = parseAmount(totals.operating);
  const investing = parseAmount(totals.investing);
  const financing = parseAmount(totals.financing);
  const netCashFlow = parseAmount(totals.net_cash_flow);
  const beginningCash = parseAmount(totals.beginning_cash);
  const endingCash = parseAmount(totals.ending_cash);

  // Find net income from operating activities
  const netIncomeEntry = operatingDetails.find(line => 
    line.label && line.label.toLowerCase().includes('net income')
  );
  const netIncome = netIncomeEntry ? parseAmount(netIncomeEntry.amount) : 0;

  // Calculate cash conversion metrics
  const cashConversionRatio = netIncome !== 0 ? (operating / netIncome) : 0;
  const cashBurnRate = operating < 0 ? Math.abs(operating) : 0;
  const cashRunway = cashBurnRate > 0 ? (endingCash / cashBurnRate) : Infinity;

  // Analyze major cash flow components
  const majorOperatingItems = operatingDetails
    .filter(line => Math.abs(parseAmount(line.amount)) > 1000)
    .map(line => ({
      description: line.label,
      amount: parseAmount(line.amount),
      path: line.path,
      impact: parseAmount(line.amount) > 0 ? 'positive' : 'negative'
    }))
    .sort((a, b) => Math.abs(b.amount) - Math.abs(a.amount))
    .slice(0, 10);

  // Calculate period-over-period change (if we have previous data)
  const { rows: previousRows } = await pg.query(`
    SELECT operating, net_cash_flow, ending_cash
    FROM cashflow_totals ct
    JOIN cashflow_reports cr ON ct.report_id = cr.id
    WHERE cr.end_date < (SELECT end_date FROM cashflow_reports WHERE id = $1)
    ORDER BY cr.end_date DESC
    LIMIT 1
  `, [reportId]);

  let periodOverPeriod = null;
  if (previousRows.length > 0) {
    const prev = previousRows[0];
    periodOverPeriod = {
      operating_change: operating - parseAmount(prev.operating),
      operating_change_percent: prev.operating !== 0 ? ((operating - parseAmount(prev.operating)) / Math.abs(parseAmount(prev.operating)) * 100) : 0,
      net_cash_change: netCashFlow - parseAmount(prev.net_cash_flow),
      ending_cash_change: endingCash - parseAmount(prev.ending_cash)
    };
  }

  // Build comprehensive cashflow KPI
  const cashflowKpi = {
    summary: {
      operating_cash_flow: operating,
      investing_cash_flow: investing,
      financing_cash_flow: financing,
      net_cash_flow: netCashFlow,
      beginning_cash: beginningCash,
      ending_cash: endingCash,
      net_income: netIncome,
      calculation_date: new Date().toISOString()
    },
    
    metrics: {
      cash_conversion_ratio: cashConversionRatio,
      cash_burn_rate: cashBurnRate,
      cash_runway_months: cashRunway === Infinity ? null : cashRunway,
      operating_margin: netIncome !== 0 ? (operating / Math.abs(netIncome) * 100) : 0,
      cash_efficiency: endingCash !== 0 ? (netCashFlow / endingCash * 100) : 0
    },

    analysis: {
      cash_position: endingCash > 100000 ? 'strong' : endingCash > 50000 ? 'adequate' : 'concerning',
      operating_trend: operating > 0 ? 'positive' : 'negative',
      liquidity_status: endingCash > Math.abs(operating) * 3 ? 'excellent' : 
                       endingCash > Math.abs(operating) ? 'good' : 'tight',
      major_operating_items: majorOperatingItems
    },

    breakdown: {
      operating_activities: {
        total: operating,
        major_items: operatingDetails.map(line => ({
          description: line.label,
          amount: parseAmount(line.amount),
          path: line.path
        })).filter(item => Math.abs(item.amount) > 500)
      },
      investing_activities: {
        total: investing,
        items: investingDetails.map(line => ({
          description: line.label,
          amount: parseAmount(line.amount),
          path: line.path
        }))
      },
      financing_activities: {
        total: financing,
        items: financingDetails.map(line => ({
          description: line.label,
          amount: parseAmount(line.amount),
          path: line.path
        }))
      }
    },

    period_comparison: periodOverPeriod
  };

  return cashflowKpi;
}

async function insertOrUpdateKPISnapshot(reportId, cashflowKpi) {
  // Check if KPI snapshot already exists for this cashflow report
  const { rows: existingRows } = await pg.query(`
    SELECT id FROM kpi_snapshots WHERE cashflow_report_id = $1
  `, [reportId]);

  if (existingRows.length > 0) {
    // Update existing snapshot
    await pg.query(`
      UPDATE kpi_snapshots 
      SET cashflow_kpi = $2, created_at = now()
      WHERE cashflow_report_id = $1
    `, [reportId, JSON.stringify(cashflowKpi)]);
    
    return existingRows[0].id;
  } else {
    // Insert new snapshot
    const { rows } = await pg.query(`
      INSERT INTO kpi_snapshots (cashflow_report_id, cashflow_kpi)
      VALUES ($1, $2)
      RETURNING id
    `, [reportId, JSON.stringify(cashflowKpi)]);
    
    return rows[0].id;
  }
}

async function getReportInfo(reportId) {
  const { rows } = await pg.query(`
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM cashflow_reports 
    WHERE id = $1
  `, [reportId]);
  
  return rows.length ? rows[0] : null;
}

async function processReport(reportId) {
  const reportInfo = await getReportInfo(reportId);
  if (!reportInfo) {
    console.log(`❌ Cashflow report ${reportId} not found`);
    return false;
  }

  console.log(
    `Processing cashflow report ${reportId}: ${reportInfo.report_name} (${reportInfo.start_date} to ${reportInfo.end_date})`
  );

  // Check if we have cashflow data for this report
  const { rows: totalsCount } = await pg.query(`
    SELECT COUNT(*) as count FROM cashflow_totals WHERE report_id = $1
  `, [reportId]);
  
  const { rows: linesCount } = await pg.query(`
    SELECT COUNT(*) as count FROM cashflow_lines WHERE report_id = $1
  `, [reportId]);
  
  console.log(`   Found ${totalsCount[0].count} cashflow totals and ${linesCount[0].count} cashflow lines`);

  if (totalsCount[0].count === 0) {
    console.log(`❌ No cashflow data found for report ${reportId}`);
    return false;
  }

  const cashflowKpi = await calculateCashflowForReport(reportId);
  
  // Validate the data
  if (typeof cashflowKpi.summary.net_cash_flow !== 'number') {
    console.log('❌ Invalid cashflow data:', cashflowKpi);
    return false;
  }
  
  const kpiId = await insertOrUpdateKPISnapshot(reportId, cashflowKpi);
  
  console.log(`✅ Cashflow KPI calculated for report ${reportId}:`);
  console.log(`   Operating Cash Flow: $${cashflowKpi.summary.operating_cash_flow.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`   Net Cash Flow: $${cashflowKpi.summary.net_cash_flow.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`   Ending Cash: $${cashflowKpi.summary.ending_cash.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`   Cash Position: ${cashflowKpi.analysis.cash_position}`);
  console.log(`   Operating Trend: ${cashflowKpi.analysis.operating_trend}`);
  console.log(`   KPI Snapshot ID: ${kpiId}`);
  
  // Show major cash flow items
  if (cashflowKpi.analysis.major_operating_items.length > 0) {
    console.log(`   Major Operating Items:`);
    cashflowKpi.analysis.major_operating_items.slice(0, 3).forEach((item, index) => {
      console.log(`     ${index + 1}. ${item.description}: $${item.amount.toLocaleString('en-US', {minimumFractionDigits: 2})} (${item.impact})`);
    });
  }
  
  console.log("");
  
  return true;
}

async function run() {
  await pg.connect();
  await ensureKPISchema();
  
  if (REPORT_ID === "all") {
    // Process all cashflow reports that don't have KPI calculated yet
    const { rows } = await pg.query(`
      SELECT cr.id 
      FROM cashflow_reports cr 
      LEFT JOIN kpi_snapshots ks ON cr.id = ks.cashflow_report_id 
      WHERE ks.cashflow_kpi IS NULL 
      ORDER BY cr.id
    `);
    
    if (!rows.length) {
      console.log("No cashflow reports found that need KPI calculation.");
      await pg.end();
      return;
    }
    
    console.log(`Found ${rows.length} cashflow reports that need KPI calculation.\n`);
    
    let processed = 0;
    for (const row of rows) {
      const success = await processReport(row.id);
      if (success) processed++;
    }
    
    console.log(`✅ Processed ${processed} cashflow reports.`);
    
  } else {
    // Process specific report or latest report
    let reportId = REPORT_ID;
    
    if (!reportId) {
      const { rows } = await pg.query(`
        SELECT id FROM cashflow_reports ORDER BY id DESC LIMIT 1
      `);
      if (!rows.length) {
        console.log("No cashflow reports found in database.");
        await pg.end();
        return;
      }
      reportId = rows[0].id;
      console.log(`No report ID specified, using latest cashflow report: ${reportId}\n`);
    }
    
    await processReport(reportId);
  }
  
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
