#!/usr/bin/env node
/**
 * Calculate EBITDA from P&L data stored in the database.
 * This script reads P&L data from the database and calculates EBITDA metrics.
 *
 * Usage:
 *   node calculate_ebitda.js [report_id]
 *
 * If no report_id is provided, calculates EBITDA for the latest report.
 * If report_id is "all", calculates EBITDA for all reports that don't have it yet.
 */

import "dotenv/config";
import { Client } from "pg";

const { PGHOST, PGPORT, PGDATABASE, PGUSER, PGPASSWORD } = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter or "all"

async function ensureEBITDASchema() {
  // EBITDA calculation table
  await pg.query(`
    create table if not exists pnl_ebitda (
      id bigserial primary key,
      report_id bigint not null unique references pnl_reports(id) on delete cascade,
      net_income numeric(18,2) not null default 0,
      interest_expense numeric(18,2) not null default 0,
      income_taxes numeric(18,2) not null default 0,
      depreciation numeric(18,2) not null default 0,
      amortization numeric(18,2) not null default 0,
      ebit numeric(18,2) not null default 0,
      ebitda numeric(18,2) not null default 0,
      created_at timestamptz not null default now()
    );
  `);

  // EBITDA mapping table for account classification
  await pg.query(`
    create table if not exists ebitda_account_map (
      id bigserial primary key,
      account_id text,
      account_name_pattern text,
      account_sub_type text,
      ebitda_category text not null check (ebitda_category in ('Interest','Taxes','Depreciation','Amortization')),
      priority int not null default 100,
      created_at timestamptz not null default now()
    );
  `);
}

async function loadEBITDAMappings() {
  const { rows } = await pg.query(`
    SELECT account_name_pattern, ebitda_category, priority
    FROM ebitda_account_map
    ORDER BY priority ASC
  `);
  return rows;
}

function classifyEBITDAAccount(accountName, mappings = []) {
  if (!accountName) return null;

  const name = accountName.toLowerCase();

  // First, try database mappings (if provided)
  for (const mapping of mappings) {
    const pattern = mapping.account_name_pattern
      .toLowerCase()
      .replace(/%/g, "");
    if (name.includes(pattern)) {
      return mapping.ebitda_category;
    }
  }

  // Fallback to hardcoded patterns
  // Interest expense patterns
  if (
    name.includes("interest expense") ||
    name.includes("interest paid") ||
    name.includes("loan interest") ||
    name.includes("financing cost")
  ) {
    return "Interest";
  }

  // Tax expense patterns
  if (
    name.includes("income tax") ||
    name.includes("tax expense") ||
    name.includes("federal tax") ||
    name.includes("state tax")
  ) {
    return "Taxes";
  }

  // Depreciation patterns
  if (name.includes("depreciation") || name.includes("deprec")) {
    return "Depreciation";
  }

  // Amortization patterns
  if (name.includes("amortization") || name.includes("amort")) {
    return "Amortization"; /*  */
  }

  return null;
}

function parseAmount(value) {
  if (value === null || value === undefined) return 0;
  const num = Number(value);
  return Number.isFinite(num) ? num : 0;
}

async function calculateEBITDAForReport(reportId) {
  // Get Net Income from summaries
  const { rows: summaryRows } = await pg.query(
    `
    SELECT amount FROM pnl_summaries
    WHERE report_id = $1 AND "group" = 'NetIncome'
    LIMIT 1
  `,
    [reportId]
  );

  const netIncome = parseAmount(summaryRows.length ? summaryRows[0].amount : 0);

  // Load EBITDA mappings from database
  const ebitdaMappings = await loadEBITDAMappings();

  // Initialize EBITDA components
  let interestExpense = 0;
  let incomeTaxes = 0;
  let depreciation = 0;
  let amortization = 0;

  // Get all expense lines for this report
  const { rows: expenseRows } = await pg.query(
    `
    SELECT account_name, amount
    FROM pnl_lines
    WHERE report_id = $1 AND path LIKE 'Expenses%'
  `,
    [reportId]
  );

  // Process all expense lines to identify EBITDA components
  for (const line of expenseRows) {
    const accountName = line.account_name;
    const amount = parseAmount(line.amount);

    // Classify expenses into EBITDA categories
    const category = classifyEBITDAAccount(accountName, ebitdaMappings);

    switch (category) {
      case "Interest":
        interestExpense += amount;
        break;
      case "Taxes":
        incomeTaxes += amount;
        break;
      case "Depreciation":
        depreciation += amount;
        break;
      case "Amortization":
        amortization += amount;
        break;
    }
  }

  // Calculate EBIT and EBITDA
  const ebit = netIncome + interestExpense + incomeTaxes;
  const ebitda = ebit + depreciation + amortization;

  return {
    net_income: netIncome,
    interest_expense: interestExpense,
    income_taxes: incomeTaxes,
    depreciation: depreciation,
    amortization: amortization,
    ebit: ebit,
    ebitda: ebitda,
  };
}

async function insertOrUpdateKPISnapshot(reportId, ebitdaData) {
  // Create comprehensive EBITDA KPI JSON
  const ebitdaKpi = {
    summary: {
      net_income: ebitdaData.net_income,
      interest_expense: ebitdaData.interest_expense,
      income_taxes: ebitdaData.income_taxes,
      depreciation: ebitdaData.depreciation,
      amortization: ebitdaData.amortization,
      ebit: ebitdaData.ebit,
      ebitda: ebitdaData.ebitda,
      calculation_date: new Date().toISOString(),
    },

    metrics: {
      ebitda_margin: null, // Will be calculated if revenue data is available
      ebit_margin: null,
      interest_coverage_ratio:
        ebitdaData.interest_expense !== 0
          ? ebitdaData.ebit / ebitdaData.interest_expense
          : null,
      tax_efficiency:
        ebitdaData.ebit !== 0
          ? (ebitdaData.income_taxes / Math.abs(ebitdaData.ebit)) * 100
          : 0,
    },

    analysis: {
      profitability_trend:
        ebitdaData.ebitda > ebitdaData.net_income ? "positive" : "concerning",
      interest_burden:
        ebitdaData.interest_expense > ebitdaData.net_income * 0.1
          ? "high"
          : "manageable",
      operational_efficiency:
        ebitdaData.ebit > 0 ? "profitable" : "loss-making",
    },

    breakdown: {
      ebitda_components: {
        net_income: ebitdaData.net_income,
        add_back_interest: ebitdaData.interest_expense,
        add_back_taxes: ebitdaData.income_taxes,
        add_back_depreciation: ebitdaData.depreciation,
        add_back_amortization: ebitdaData.amortization,
      },
    },
  };

  // Check if KPI snapshot already exists for this P&L report
  const { rows: existingRows } = await pg.query(
    `
    SELECT id FROM kpi_snapshots WHERE pnl_report_id = $1
  `,
    [reportId]
  );

  if (existingRows.length > 0) {
    // Update existing snapshot
    await pg.query(
      `
      UPDATE kpi_snapshots
      SET ebitda_kpi = $2, created_at = now()
      WHERE pnl_report_id = $1
    `,
      [reportId, JSON.stringify(ebitdaKpi)]
    );

    return existingRows[0].id;
  } else {
    // Insert new snapshot
    const { rows } = await pg.query(
      `
      INSERT INTO kpi_snapshots (pnl_report_id, ebitda_kpi)
      VALUES ($1, $2)
      RETURNING id
    `,
      [reportId, JSON.stringify(ebitdaKpi)]
    );

    return rows[0].id;
  }
}

async function getReportInfo(reportId) {
  const { rows } = await pg.query(
    `
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM pnl_reports 
    WHERE id = $1
  `,
    [reportId]
  );

  return rows.length ? rows[0] : null;
}

async function processReport(reportId) {
  const reportInfo = await getReportInfo(reportId);
  if (!reportInfo) {
    console.log(`❌ Report ${reportId} not found`);
    return false;
  }

  console.log(
    `Processing report ${reportId}: ${reportInfo.report_name} (${reportInfo.start_date} to ${reportInfo.end_date})`
  );

  // Check if we have any data for this report
  const { rows: lineCount } = await pg.query(
    `
    SELECT COUNT(*) as count FROM pnl_lines WHERE report_id = $1
  `,
    [reportId]
  );

  const { rows: summaryCount } = await pg.query(
    `
    SELECT COUNT(*) as count FROM pnl_summaries WHERE report_id = $1
  `,
    [reportId]
  );

  console.log(
    `   Found ${lineCount[0].count} P&L lines and ${summaryCount[0].count} summaries`
  );

  const ebitdaData = await calculateEBITDAForReport(reportId);

  // Validate the data before trying to format it
  if (
    typeof ebitdaData.net_income !== "number" ||
    typeof ebitdaData.interest_expense !== "number" ||
    typeof ebitdaData.income_taxes !== "number" ||
    typeof ebitdaData.depreciation !== "number" ||
    typeof ebitdaData.amortization !== "number" ||
    typeof ebitdaData.ebit !== "number" ||
    typeof ebitdaData.ebitda !== "number"
  ) {
    console.log("❌ Invalid EBITDA data types:", ebitdaData);
    return false;
  }

  const kpiId = await insertOrUpdateKPISnapshot(reportId, ebitdaData);

  console.log(`✅ EBITDA calculated for report ${reportId}:`);
  console.log(`   Net Income: $${ebitdaData.net_income.toFixed(2)}`);
  console.log(
    `   Interest Expense: $${ebitdaData.interest_expense.toFixed(2)}`
  );
  console.log(`   Income Taxes: $${ebitdaData.income_taxes.toFixed(2)}`);
  console.log(`   Depreciation: $${ebitdaData.depreciation.toFixed(2)}`);
  console.log(`   Amortization: $${ebitdaData.amortization.toFixed(2)}`);
  console.log(`   EBIT: $${ebitdaData.ebit.toFixed(2)}`);
  console.log(`   EBITDA: $${ebitdaData.ebitda.toFixed(2)}`);
  console.log(`   KPI Snapshot ID: ${kpiId}`);
  console.log("");

  return true;
}

async function run() {
  await pg.connect();
  await ensureEBITDASchema();

  if (REPORT_ID === "all") {
    // Process all reports that don't have EBITDA calculated yet
    const { rows } = await pg.query(`
      SELECT pr.id 
      FROM pnl_reports pr 
      LEFT JOIN pnl_ebitda pe ON pr.id = pe.report_id 
      WHERE pe.report_id IS NULL 
      ORDER BY pr.id
    `);

    if (!rows.length) {
      console.log("No reports found that need EBITDA calculation.");
      await pg.end();
      return;
    }

    console.log(`Found ${rows.length} reports that need EBITDA calculation.\n`);

    let processed = 0;
    for (const row of rows) {
      const success = await processReport(row.id);
      if (success) processed++;
    }

    console.log(`✅ Processed ${processed} reports.`);
  } else {
    // Process specific report or latest report
    let reportId = REPORT_ID;

    if (!reportId) {
      const { rows } = await pg.query(`
        SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1
      `);
      if (!rows.length) {
        console.log("No P&L reports found in database.");
        await pg.end();
        return;
      }
      reportId = rows[0].id;
      console.log(`No report ID specified, using latest report: ${reportId}\n`);
    }

    await processReport(reportId);
  }

  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
