#!/usr/bin/env node
/**
 * Calculate total revenue from P&L data stored in the database.
 * This script reads P&L data from the database and calculates detailed revenue metrics.
 *
 * Usage:
 *   node calculate_revenue.js [report_id]
 *
 * If no report_id is provided, calculates revenue for the latest report.
 * If report_id is "all", calculates revenue for all reports that don't have it yet.
 */

import "dotenv/config";
import { Client } from "pg";

const { PGHOST, PGPORT, PGDATABASE, PGUSER, PGPASSWORD } = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter or "all"

async function ensureKPISchema() {
  // KPI snapshots table
  await pg.query(`
    create table if not exists kpi_snapshots (
      id bigserial primary key,
      pnl_report_id bigint references pnl_reports(id) on delete set null,
      cashflow_report_id bigint references cashflow_reports(id) on delete set null,
      
      -- KPI payloads stored as JSONB for flexibility
      total_expense_kpi jsonb,
      total_revenue_kpi jsonb,
      ebitda_kpi jsonb,
      cashflow_kpi jsonb,
      
      -- timestamp for when this KPI snapshot was created
      created_at timestamptz not null default now()
    );
  `);

  // Create index for faster lookups
  await pg.query(`
    create index if not exists idx_kpi_snapshots_pnl_report_id 
    on kpi_snapshots(pnl_report_id);
  `);
}

function parseAmount(value) {
  if (value === null || value === undefined) return 0;
  const num = Number(value);
  return Number.isFinite(num) ? num : 0;
}

async function calculateRevenueForReport(reportId) {
  // Get all income lines for this report
  const { rows: incomeRows } = await pg.query(
    `
    SELECT account_name, amount, path
    FROM pnl_lines 
    WHERE report_id = $1 AND path LIKE 'Income%'
    ORDER BY path, account_name
  `,
    [reportId]
  );

  // Initialize revenue components
  const revenueBreakdown = {
    practice_income: {
      doctors: {},
      office_staff: 0,
      total: 0,
    },
    other_income: {
      items: {},
      total: 0,
    },
    total_revenue: 0,
  };

  let totalRevenue = 0;

  // Process all income lines
  for (const line of incomeRows) {
    const accountName = line.account_name;
    const amount = parseAmount(line.amount);
    const path = line.path || "";

    totalRevenue += amount;

    // Categorize income based on path patterns (since account names are generic)
    if (path.includes("Dr.") && path.includes("Income")) {
      // Extract doctor name from path
      const doctorMatch = path.match(/Dr\.\s*(\w+)/);
      const doctorName = doctorMatch ? doctorMatch[1] : "Unknown";

      if (!revenueBreakdown.practice_income.doctors[doctorName]) {
        revenueBreakdown.practice_income.doctors[doctorName] = 0;
      }
      revenueBreakdown.practice_income.doctors[doctorName] += amount;
      revenueBreakdown.practice_income.total += amount;
    } else if (path.includes("Office") && path.includes("Staff")) {
      revenueBreakdown.practice_income.office_staff += amount;
      revenueBreakdown.practice_income.total += amount;
    } else if (path.includes("Practice Income")) {
      // Other practice income that doesn't match doctor or staff patterns
      revenueBreakdown.practice_income.total += amount;
    } else if (accountName && !accountName.includes("Total")) {
      // Other income items (non-practice income)
      revenueBreakdown.other_income.items[accountName] = amount;
      revenueBreakdown.other_income.total += amount;
    }
  }

  revenueBreakdown.total_revenue = totalRevenue;

  // Calculate additional metrics
  const metrics = {
    revenue_by_doctor: Object.entries(revenueBreakdown.practice_income.doctors)
      .map(([doctor, amount]) => ({
        doctor,
        amount,
        percentage: totalRevenue > 0 ? (amount / totalRevenue) * 100 : 0,
      }))
      .sort((a, b) => b.amount - a.amount),

    practice_vs_other: {
      practice_income: revenueBreakdown.practice_income.total,
      practice_percentage:
        totalRevenue > 0
          ? (revenueBreakdown.practice_income.total / totalRevenue) * 100
          : 0,
      other_income: revenueBreakdown.other_income.total,
      other_percentage:
        totalRevenue > 0
          ? (revenueBreakdown.other_income.total / totalRevenue) * 100
          : 0,
    },

    top_revenue_sources: [
      ...Object.entries(revenueBreakdown.practice_income.doctors).map(
        ([name, amount]) => ({
          source: `Dr. ${name}`,
          amount,
          category: "Practice Income",
        })
      ),
      ...Object.entries(revenueBreakdown.other_income.items).map(
        ([name, amount]) => ({
          source: name,
          amount,
          category: "Other Income",
        })
      ),
    ]
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 10),
  };

  return {
    summary: {
      total_revenue: totalRevenue,
      practice_income: revenueBreakdown.practice_income.total,
      other_income: revenueBreakdown.other_income.total,
      number_of_doctors: Object.keys(revenueBreakdown.practice_income.doctors)
        .length,
      calculation_date: new Date().toISOString(),
    },
    breakdown: revenueBreakdown,
    metrics: metrics,
  };
}

async function insertOrUpdateKPISnapshot(reportId, revenueKpi) {
  // Check if KPI snapshot already exists for this report
  const { rows: existingRows } = await pg.query(
    `
    SELECT id FROM kpi_snapshots WHERE pnl_report_id = $1
  `,
    [reportId]
  );

  if (existingRows.length > 0) {
    // Update existing snapshot
    await pg.query(
      `
      UPDATE kpi_snapshots 
      SET total_revenue_kpi = $2, created_at = now()
      WHERE pnl_report_id = $1
    `,
      [reportId, JSON.stringify(revenueKpi)]
    );

    return existingRows[0].id;
  } else {
    // Insert new snapshot
    const { rows } = await pg.query(
      `
      INSERT INTO kpi_snapshots (pnl_report_id, total_revenue_kpi)
      VALUES ($1, $2)
      RETURNING id
    `,
      [reportId, JSON.stringify(revenueKpi)]
    );

    return rows[0].id;
  }
}

async function getReportInfo(reportId) {
  const { rows } = await pg.query(
    `
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM pnl_reports 
    WHERE id = $1
  `,
    [reportId]
  );

  return rows.length ? rows[0] : null;
}

async function processReport(reportId) {
  const reportInfo = await getReportInfo(reportId);
  if (!reportInfo) {
    console.log(`❌ Report ${reportId} not found`);
    return false;
  }

  console.log(
    `Processing report ${reportId}: ${reportInfo.report_name} (${reportInfo.start_date} to ${reportInfo.end_date})`
  );

  // Check if we have any income data for this report
  const { rows: incomeCount } = await pg.query(
    `
    SELECT COUNT(*) as count FROM pnl_lines 
    WHERE report_id = $1 AND path LIKE 'Income%'
  `,
    [reportId]
  );

  console.log(`   Found ${incomeCount[0].count} income lines`);

  const revenueKpi = await calculateRevenueForReport(reportId);

  // Validate the data
  if (typeof revenueKpi.summary.total_revenue !== "number") {
    console.log("❌ Invalid revenue data:", revenueKpi);
    return false;
  }

  const kpiId = await insertOrUpdateKPISnapshot(reportId, revenueKpi);

  console.log(`✅ Revenue KPI calculated for report ${reportId}:`);
  console.log(
    `   Total Revenue: $${revenueKpi.summary.total_revenue.toLocaleString(
      "en-US",
      { minimumFractionDigits: 2 }
    )}`
  );
  console.log(
    `   Practice Income: $${revenueKpi.summary.practice_income.toLocaleString(
      "en-US",
      { minimumFractionDigits: 2 }
    )} (${revenueKpi.metrics.practice_vs_other.practice_percentage.toFixed(
      1
    )}%)`
  );
  console.log(
    `   Other Income: $${revenueKpi.summary.other_income.toLocaleString(
      "en-US",
      { minimumFractionDigits: 2 }
    )} (${revenueKpi.metrics.practice_vs_other.other_percentage.toFixed(1)}%)`
  );
  console.log(`   Number of Doctors: ${revenueKpi.summary.number_of_doctors}`);
  console.log(`   KPI Snapshot ID: ${kpiId}`);

  // Show top revenue sources
  console.log(`   Top Revenue Sources:`);
  revenueKpi.metrics.top_revenue_sources
    .slice(0, 5)
    .forEach((source, index) => {
      console.log(
        `     ${index + 1}. ${source.source}: $${source.amount.toLocaleString(
          "en-US",
          { minimumFractionDigits: 2 }
        )}`
      );
    });

  console.log("");

  return true;
}

async function run() {
  await pg.connect();
  await ensureKPISchema();

  if (REPORT_ID === "all") {
    // Process all reports that don't have revenue KPI calculated yet
    const { rows } = await pg.query(`
      SELECT pr.id 
      FROM pnl_reports pr 
      LEFT JOIN kpi_snapshots ks ON pr.id = ks.pnl_report_id 
      WHERE ks.total_revenue_kpi IS NULL 
      ORDER BY pr.id
    `);

    if (!rows.length) {
      console.log("No reports found that need revenue KPI calculation.");
      await pg.end();
      return;
    }

    console.log(
      `Found ${rows.length} reports that need revenue KPI calculation.\n`
    );

    let processed = 0;
    for (const row of rows) {
      const success = await processReport(row.id);
      if (success) processed++;
    }

    console.log(`✅ Processed ${processed} reports.`);
  } else {
    // Process specific report or latest report
    let reportId = REPORT_ID;

    if (!reportId) {
      const { rows } = await pg.query(`
        SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1
      `);
      if (!rows.length) {
        console.log("No P&L reports found in database.");
        await pg.end();
        return;
      }
      reportId = rows[0].id;
      console.log(`No report ID specified, using latest report: ${reportId}\n`);
    }

    await processReport(reportId);
  }

  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
