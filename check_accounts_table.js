#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

async function checkAccountsTable() {
  await pg.connect();

  // Check if accounts table exists and its structure
  try {
    const { rows } = await pg.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'accounts' 
      ORDER BY ordinal_position
    `);
    
    if (rows.length === 0) {
      console.log('❌ accounts table does not exist');
    } else {
      console.log('📋 accounts table structure:');
      rows.forEach(row => {
        console.log(`  ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }
    
    // Check constraints
    const { rows: constraints } = await pg.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'accounts'
    `);
    
    console.log('\n🔒 Constraints:');
    if (constraints.length === 0) {
      console.log('  No constraints found - THIS IS THE PROBLEM!');
    } else {
      constraints.forEach(row => {
        console.log(`  ${row.constraint_name}: ${row.constraint_type}`);
      });
    }
    
    // Check indexes
    const { rows: indexes } = await pg.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'accounts'
    `);
    
    console.log('\n📊 Indexes:');
    if (indexes.length === 0) {
      console.log('  No indexes found');
    } else {
      indexes.forEach(row => {
        console.log(`  ${row.indexname}: ${row.indexdef}`);
      });
    }
    
  } catch (e) {
    console.error('❌ Error checking table:', e.message);
  }

  await pg.end();
}

checkAccountsTable().catch(console.error);
