"id","name","fully_qualified_name","account_type","account_sub_type","classification","active","currency","raw","updated_at","created_at"
"267","401K","401K",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""267"", ""Name"": ""401K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:33:12-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -3710.52, ""FullyQualifiedName"": ""401K"", ""CurrentBalanceWithSubAccounts"": -5949.9}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"368","401K - Associate Dr. George","401K:401K - Associate Dr. George",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""368"", ""Name"": ""401K - Associate Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T14:15:00-08:00"", ""LastUpdatedTime"": ""2024-02-13T14:15:00-08:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""401K:401K - Associate Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"291","401K - Dr. Canner","401K:401K - Dr. Canner",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""291"", ""Name"": ""401K - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:42:33-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Canner"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -576.92, ""FullyQualifiedName"": ""401K:401K - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": -576.92}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"292","401K - Dr. George","401K:401K - Dr. George",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""292"", ""Name"": ""401K - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:43:07-08:00"", ""LastUpdatedTime"": ""2024-08-20T14:58:36-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. George"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""401K:401K - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"356","401K - Dr. Ghosh","401K:401K - Dr. Ghosh",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""356"", ""Name"": ""401K - Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-09T08:35:12-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -903.84, ""FullyQualifiedName"": ""401K:401K - Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": -903.84}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"294","401K - Dr. Pierce","401K:401K - Dr. Pierce",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""294"", ""Name"": ""401K - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:43:57-08:00"", ""LastUpdatedTime"": ""2024-07-12T07:24:11-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Pierce"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""401K:401K - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"295","401K - Dr. Scherer","401K:401K - Dr. Scherer",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""295"", ""Name"": ""401K - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:44:22-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Scherer"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -903.84, ""FullyQualifiedName"": ""401K:401K - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": -903.84}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"296","401K - Dr. Shah","401K:401K - Dr. Shah",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""296"", ""Name"": ""401K - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:44:40-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Shah"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -903.84, ""FullyQualifiedName"": ""401K:401K - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": -903.84}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********","401K - Dr. Shaikh","401K:401K - Dr. Shaikh",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""**********"", ""Name"": ""401K - Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2025-06-10T06:13:25-07:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 2473.68, ""FullyQualifiedName"": ""401K:401K - Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 2473.68}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"289","401K - Office Staff","401K:401K - Office Staff",Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""289"", ""Name"": ""401K - Office Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:39:47-08:00"", ""LastUpdatedTime"": ""2025-08-29T06:01:39-07:00""}, ""ParentRef"": {""value"": ""267""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Office Staff"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -1424.62, ""FullyQualifiedName"": ""401K:401K - Office Staff"", ""CurrentBalanceWithSubAccounts"": -1424.62}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"81",Accounts Payable,Accounts Payable,Accounts Payable,AccountsPayable,Liability,true,USD,"{""Id"": ""81"", ""Name"": ""Accounts Payable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2025-08-06T05:44:07-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Accounts Payable"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""AccountsPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": -3574.43, ""FullyQualifiedName"": ""Accounts Payable"", ""CurrentBalanceWithSubAccounts"": -3574.43}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"102",Accounts Receivable,Accounts Receivable,Accounts Receivable,AccountsReceivable,Asset,true,USD,"{""Id"": ""102"", ""Name"": ""Accounts Receivable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""1200"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:28:49-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Accounts Receivable"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""AccountsReceivable"", ""Classification"": ""Asset"", ""CurrentBalance"": 1405560.29, ""FullyQualifiedName"": ""Accounts Receivable"", ""CurrentBalanceWithSubAccounts"": 1405560.29}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"80",Accumulated Depreciation,Accumulated Depreciation,Fixed Asset,OtherFixedAssets,Asset,true,USD,"{""Id"": ""80"", ""Name"": ""Accumulated Depreciation"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2025-07-10T09:01:30-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Fixed Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherFixedAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": -313792.43, ""FullyQualifiedName"": ""Accumulated Depreciation"", ""CurrentBalanceWithSubAccounts"": -313792.43}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"124",Actuary,Actuary,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""124"", ""Name"": ""Actuary"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Annual rpt/ProfitSharePlan"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Actuary"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"35",Advertising & Promotion,Advertising & Promotion,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""35"", ""Name"": ""Advertising & Promotion"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:31:47-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Advertising & Promotion"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"372",Ask My Accountant,Ask My Accountant,Expense,OtherBusinessExpenses,,true,USD,"{""Id"": ""372"", ""Name"": ""Ask My Accountant"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-09T07:59:58-07:00"", ""LastUpdatedTime"": ""2024-08-09T07:59:58-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherBusinessExpenses"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Ask My Accountant"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"69",Associate Fees,Associate Fees,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""69"", ""Name"": ""Associate Fees"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Associate Fees"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"55",Auto,Auto,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""55"", ""Name"": ""Auto"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Auto"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"57",Auto - Dr. G,Auto - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""57"", ""Name"": ""Auto - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Auto - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"56",Auto Dr. K,Auto Dr. K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""56"", ""Name"": ""Auto Dr. K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Auto Dr. K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"34",Bank Charge,Bank Charge,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""34"", ""Name"": ""Bank Charge"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Bank Charge"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"179",Bank error,Bank error,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""179"", ""Name"": ""Bank error"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Bank error"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"98",Bank Loan,Bank Loan,Other Current Liability,LoanPayable,Liability,true,USD,"{""Id"": ""98"", ""Name"": ""Bank Loan"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:04:57-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""LoanPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": -719323.92, ""FullyQualifiedName"": ""Bank Loan"", ""CurrentBalanceWithSubAccounts"": -719323.92}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"241",BDS Distribution,BDS Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""241"", ""Name"": ""BDS Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:50:36-08:00"", ""LastUpdatedTime"": ""2025-02-08T15:27:19-08:00""}, ""SyncToken"": ""3"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""2022 distribution"", ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 65000, ""FullyQualifiedName"": ""BDS Distribution"", ""CurrentBalanceWithSubAccounts"": 65000}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"230",Billable Expense Income,Billable Expense Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""230"", ""Name"": ""Billable Expense Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:40:44-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:40:44-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Billable Expense Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"101",Billing Company,Billing Company,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""101"", ""Name"": ""Billing Company"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Billing Company"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"107",Board Certification,Board Certification,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""107"", ""Name"": ""Board Certification"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Board Certification"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"121",Business Expense,Business Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""121"", ""Name"": ""Business Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Misc. Business expenses"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"140",Business Expense - Dr. George,Business Expense - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""140"", ""Name"": ""Business Expense - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"141",Business Expense - Dr. Kreiling,Business Expense - Dr. Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""141"", ""Name"": ""Business Expense - Dr. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - Dr. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"142",Business Expense - Dr. Pierce,Business Expense - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""142"", ""Name"": ""Business Expense - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"143",Business Expense - Dr. Scherer,Business Expense - Dr. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""143"", ""Name"": ""Business Expense - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"201",Business Expense - Dr. Shah,Business Expense - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""201"", ""Name"": ""Business Expense - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"202",Business Expense - JC,Business Expense - JC,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""202"", ""Name"": ""Business Expense - JC"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Expense - JC"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"94",Business Meals - Staff,Business Meals - Staff,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""94"", ""Name"": ""Business Meals - Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Business Meals - Staff"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"123",CCPA Income,CCPA Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""123"", ""Name"": ""CCPA Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CCPA Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"346",Charitable Contributions,Charitable Contributions,Expense,CharitableContributions,Expense,true,USD,"{""Id"": ""346"", ""Name"": ""Charitable Contributions"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-10-04T06:35:08-07:00"", ""LastUpdatedTime"": ""2023-10-04T06:35:08-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""CharitableContributions"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Charitable Contributions"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Chase credit card #4012,Chase credit card #4012,Credit Card,CreditCard,Liability,true,USD,"{""Id"": ""**********"", ""Name"": ""Chase credit card #4012"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-07T11:59:30-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:52:16-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Credit Card"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""CreditCard"", ""Classification"": ""Liability"", ""CurrentBalance"": -91295.94, ""FullyQualifiedName"": ""Chase credit card #4012"", ""CurrentBalanceWithSubAccounts"": -91295.94}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"110",Cleaning - office,Cleaning - office,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""110"", ""Name"": ""Cleaning - office"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Cleaning - office"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"203",Clinical Tool Licensure,Clinical Tool Licensure,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""203"", ""Name"": ""Clinical Tool Licensure"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2023-04-25T10:30:37-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Clinical Tool Licensure"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"116",CME,CME,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""116"", ""Name"": ""CME"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"168",CME - Dr. Canner,CME - Dr. Canner,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""168"", ""Name"": ""CME - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"132",CME - Dr. George,CME - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""132"", ""Name"": ""CME - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"133",CME - Dr. Kreiling,CME - Dr. Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""133"", ""Name"": ""CME - Dr. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"134",CME - Dr. Pierce,CME - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""134"", ""Name"": ""CME - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"135",CME - Dr. Scherer,CME - Dr. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""135"", ""Name"": ""CME - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"215",CME - Dr. Shah,CME - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""215"", ""Name"": ""CME - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""CME - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"184",Collections Company,Collections Company,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""184"", ""Name"": ""Collections Company"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Collections Company"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"86",Common Stock,Common Stock,Equity,OwnersEquity,Equity,true,USD,"{""Id"": ""86"", ""Name"": ""Common Stock"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OwnersEquity"", ""Classification"": ""Equity"", ""CurrentBalance"": 32.15, ""FullyQualifiedName"": ""Common Stock"", ""CurrentBalanceWithSubAccounts"": 32.15}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"28",Computer Equipment,Computer Equipment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""28"", ""Name"": ""Computer Equipment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Computer Equipment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Computer Equipment - Dr. Canner,Computer Equipment - Dr. Canner,Other Expense,OtherMiscellaneousExpense,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Computer Equipment - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2025-01-13T08:27:01-08:00"", ""LastUpdatedTime"": ""2025-01-13T08:27:01-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousExpense"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Computer Equipment - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"190",Computer Hardware,Computer Hardware,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""190"", ""Name"": ""Computer Hardware"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Computer Hardware"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"181",Computer Labor & Maintenance,Computer Labor & Maintenance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""181"", ""Name"": ""Computer Labor & Maintenance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Computer Labor & Maintenance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"191",Computer Software,Computer Software,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""191"", ""Name"": ""Computer Software"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Computer Software"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"207",Conference Expense,Conference Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""207"", ""Name"": ""Conference Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"212",Conference Expense - B. Scherer,Conference Expense - B. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""212"", ""Name"": ""Conference Expense - B. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - B. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"210",Conference Expense - J. Canner,Conference Expense - J. Canner,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""210"", ""Name"": ""Conference Expense - J. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - J. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"208",Conference Expense - K.Kreiling,Conference Expense - K.Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""208"", ""Name"": ""Conference Expense - K.Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - K.Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"209",Conference Expense - L.George,Conference Expense - L.George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""209"", ""Name"": ""Conference Expense - L.George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - L.George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"211",Conference Expense - M.Pierce,Conference Expense - M.Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""211"", ""Name"": ""Conference Expense - M.Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - M.Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"213",Conference Expense - R. Shah,Conference Expense - R. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""213"", ""Name"": ""Conference Expense - R. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Conference Expense - R. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"183",Cost of Goods Sold,Cost of Goods Sold,Cost of Goods Sold,SuppliesMaterialsCogs,Expense,true,USD,"{""Id"": ""183"", ""Name"": ""Cost of Goods Sold"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""50000"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Cost of Goods Sold"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Costs of items purchased and then sold to customers"", ""AccountSubType"": ""SuppliesMaterialsCogs"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Cost of Goods Sold"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"120",credit adjustment,credit adjustment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""120"", ""Name"": ""credit adjustment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""credit adjustment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"90",Credit card account,Credit card account,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""90"", ""Name"": ""Credit card account"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Credit card account"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"278",Credit Card Fees,Credit Card Fees,Expense,OtherBusinessExpenses,,true,USD,"{""Id"": ""278"", ""Name"": ""Credit Card Fees"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T10:02:26-08:00"", ""LastUpdatedTime"": ""2023-02-27T10:02:26-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Credit Card Fees"", ""AccountSubType"": ""OtherBusinessExpenses"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Credit Card Fees"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"216",Credit Card Rewards,Credit Card Rewards,Income,OtherPrimaryIncome,Revenue,true,USD,"{""Id"": ""216"", ""Name"": ""Credit Card Rewards"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2024-08-21T11:02:09-07:00""}, ""SyncToken"": ""4"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherPrimaryIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Credit Card Rewards"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"199",Dental Insurance - B. Scherer,Dental Insurance - B. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""199"", ""Name"": ""Dental Insurance - B. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dental Insurance - B. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"197",Dental Insurance - K. Kreiling,Dental Insurance - K. Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""197"", ""Name"": ""Dental Insurance - K. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dental Insurance - K. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"198",Dental Insurance - M. Pierce,Dental Insurance - M. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""198"", ""Name"": ""Dental Insurance - M. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dental Insurance - M. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"74",Depreciation Expense,Depreciation Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""74"", ""Name"": ""Depreciation Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Depreciation Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"367",DG Distribution,DG Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""367"", ""Name"": ""DG Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T14:01:30-08:00"", ""LastUpdatedTime"": ""2025-01-07T08:12:09-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 20000, ""FullyQualifiedName"": ""DG Distribution"", ""CurrentBalanceWithSubAccounts"": 20000}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Direct Deposit Liabilities,Direct Deposit Liabilities,Other Current Liability,PayrollClearing,Liability,true,USD,"{""Id"": ""**********"", ""Name"": ""Direct Deposit Liabilities"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-21T08:04:37-07:00"", ""LastUpdatedTime"": ""2025-08-28T07:15:33-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollClearing"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Direct Deposit Liabilities"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"41",Disability Insurance,Disability Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""41"", ""Name"": ""Disability Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Disability Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"43",Disability Insurance - Dr. G,Disability Insurance - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""43"", ""Name"": ""Disability Insurance - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Disability Insurance - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"42",Disability Insurance - Dr. K,Disability Insurance - Dr. K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""42"", ""Name"": ""Disability Insurance - Dr. K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Disability Insurance - Dr. K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"117",Disbursement,Disbursement,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""117"", ""Name"": ""Disbursement"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Disbursement"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Expense - Canner,Doctor Admin Income Expense - Canner,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Expense - Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:05:38-07:00"", ""LastUpdatedTime"": ""2024-08-19T14:05:38-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Doctor Admin Income Expense - Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Expense - Ghosh,Doctor Admin Income Expense - Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Expense - Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:05:01-07:00"", ""LastUpdatedTime"": ""2024-08-19T14:05:01-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Doctor Admin Income Expense - Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Expense - Pierce,Doctor Admin Income Expense - Pierce,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Expense - Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:05:13-07:00"", ""LastUpdatedTime"": ""2024-08-19T14:05:13-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Doctor Admin Income Expense - Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Expense - Scherer,Doctor Admin Income Expense - Scherer,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Expense - Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:05:26-07:00"", ""LastUpdatedTime"": ""2024-08-19T14:05:26-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Doctor Admin Income Expense - Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Expense - Shah,Doctor Admin Income Expense - Shah,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Expense - Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:05:50-07:00"", ""LastUpdatedTime"": ""2024-08-19T14:05:50-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Doctor Admin Income Expense - Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"**********",Doctor Admin Income Payable,Doctor Admin Income Payable,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""**********"", ""Name"": ""Doctor Admin Income Payable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-08-19T14:00:57-07:00"", ""LastUpdatedTime"": ""2025-08-14T13:55:04-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -4425, ""FullyQualifiedName"": ""Doctor Admin Income Payable"", ""CurrentBalanceWithSubAccounts"": -4425}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"106",Donation,Donation,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""106"", ""Name"": ""Donation"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Donation"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"14",Drugs,Drugs,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""14"", ""Name"": ""Drugs"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Drugs"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"237","Dues, Sub & Lic Deb Ghosh","Dues, Sub & Lic Deb Ghosh",Expense,AdvertisingPromotional,Expense,true,USD,"{""Id"": ""237"", ""Name"": ""Dues, Sub & Lic Deb Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T13:20:58-08:00"", ""LastUpdatedTime"": ""2023-01-08T13:20:58-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""AdvertisingPromotional"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Sub & Lic Deb Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"163","Dues, Subs & Lic - Dr. Canner","Dues, Subs & Lic - Dr. Canner",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""163"", ""Name"": ""Dues, Subs & Lic - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"54","Dues, Subs & Lic - Dr. George","Dues, Subs & Lic - Dr. George",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""54"", ""Name"": ""Dues, Subs & Lic - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"53","Dues, Subs & Lic - Dr. Kreiling","Dues, Subs & Lic - Dr. Kreiling",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""53"", ""Name"": ""Dues, Subs & Lic - Dr. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"136","Dues, Subs & Lic - Dr. Pierce","Dues, Subs & Lic - Dr. Pierce",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""136"", ""Name"": ""Dues, Subs & Lic - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"137","Dues, Subs & Lic - Dr. Scherer","Dues, Subs & Lic - Dr. Scherer",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""137"", ""Name"": ""Dues, Subs & Lic - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"185","Dues, Subs & Lic - Dr. Shah","Dues, Subs & Lic - Dr. Shah",Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""185"", ""Name"": ""Dues, Subs & Lic - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"355","Dues, Subs & Lic - Dr. Shaikh","Dues, Subs & Lic - Dr. Shaikh",Expense,DuesSubscriptions,Expense,true,USD,"{""Id"": ""355"", ""Name"": ""Dues, Subs & Lic - Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-01-13T11:18:32-08:00"", ""LastUpdatedTime"": ""2024-01-13T11:18:32-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""DuesSubscriptions"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Dues, Subs & Lic - Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"192",EHR,EHR,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""192"", ""Name"": ""EHR"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""EHR"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"144",Employee Gifts,Employee Gifts,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""144"", ""Name"": ""Employee Gifts"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:36:02-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Employee Gifts"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"238",Employee Holiday Bonus,Employee Holiday Bonus,Expense,AdvertisingPromotional,Expense,true,USD,"{""Id"": ""238"", ""Name"": ""Employee Holiday Bonus"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:36:39-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:34:28-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""AdvertisingPromotional"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Employee Holiday Bonus"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"206",EMR Add-Ins,EMR Add-Ins,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""206"", ""Name"": ""EMR Add-Ins"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""EMR Add-Ins"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"78",Equipment,Equipment,Fixed Asset,OtherFixedAssets,Asset,true,USD,"{""Id"": ""78"", ""Name"": ""Equipment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2024-12-11T15:43:19-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Fixed Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherFixedAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": 9189.8, ""FullyQualifiedName"": ""Equipment"", ""CurrentBalanceWithSubAccounts"": 9189.8}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"70",Equipment Lease,Equipment Lease,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""70"", ""Name"": ""Equipment Lease"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Equipment Lease"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"114",Equipment Maint. & Repair,Equipment Maint. & Repair,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""114"", ""Name"": ""Equipment Maint. & Repair"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:50:00-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Equipment Maint. & Repair"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Equipment Maint. & Repair"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"160",Equipment Maintenance & Toner,Equipment Maintenance & Toner,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""160"", ""Name"": ""Equipment Maintenance & Toner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Equipment Maintenance & Toner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"260",Expensed FF&E,Expensed FF&E,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""260"", ""Name"": ""Expensed FF&E"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:38:46-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:41:48-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Expensed FF&E"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Expensed FF&E"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"340",Federal UC Deposit,Federal UC Deposit,Other Asset,OrganizationalCosts,Asset,true,USD,"{""Id"": ""340"", ""Name"": ""Federal UC Deposit"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-08-30T10:06:54-07:00"", ""LastUpdatedTime"": ""2023-08-30T10:06:54-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OrganizationalCosts"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Federal UC Deposit"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"77",Furniture,Furniture,Fixed Asset,OtherFixedAssets,Asset,true,USD,"{""Id"": ""77"", ""Name"": ""Furniture"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2025-02-08T15:27:19-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Fixed Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherFixedAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": 129388.44, ""FullyQualifiedName"": ""Furniture"", ""CurrentBalanceWithSubAccounts"": 129388.44}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"38",Health Insurance,Health Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""38"", ""Name"": ""Health Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2023-09-24T10:13:01-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"223",Health Insurance - A. Theotikos,Health Insurance - A. Theotikos,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""223"", ""Name"": ""Health Insurance - A. Theotikos"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - A. Theotikos"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"220",Health Insurance - B. Rysiewicz,Health Insurance - B. Rysiewicz,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""220"", ""Name"": ""Health Insurance - B. Rysiewicz"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - B. Rysiewicz"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"149",Health Insurance - B. Scherer,Health Insurance - B. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""149"", ""Name"": ""Health Insurance - B. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - B. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"108",Health Insurance - Employee,Health Insurance - Employee,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""108"", ""Name"": ""Health Insurance - Employee"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - Employee"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"176",Health Insurance - H. Holoubek,Health Insurance - H. Holoubek,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""176"", ""Name"": ""Health Insurance - H. Holoubek"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - H. Holoubek"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"371",Health Insurance - J. Canner,Health Insurance - J. Canner,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""371"", ""Name"": ""Health Insurance - J. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-07-15T09:42:53-07:00"", ""LastUpdatedTime"": ""2024-07-15T10:13:38-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - J. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"200",Health Insurance - J. Cernetich,Health Insurance - J. Cernetich,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""200"", ""Name"": ""Health Insurance - J. Cernetich"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - J. Cernetich"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"147",Health Insurance - K. Kreiling,Health Insurance - K. Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""147"", ""Name"": ""Health Insurance - K. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - K. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"345",Health Insurance - K. OBrien,Health Insurance - K. OBrien,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""345"", ""Name"": ""Health Insurance - K. OBrien"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-09-21T04:06:27-07:00"", ""LastUpdatedTime"": ""2023-09-24T10:13:01-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - K. OBrien"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"40",Health Insurance - L. Geroge,Health Insurance - L. Geroge,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""40"", ""Name"": ""Health Insurance - L. Geroge"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - L. Geroge"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"148",Health Insurance - M. Pierce,Health Insurance - M. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""148"", ""Name"": ""Health Insurance - M. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - M. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"150",Health Insurance - M.K. Tanka,Health Insurance - M.K. Tanka,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""150"", ""Name"": ""Health Insurance - M.K. Tanka"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - M.K. Tanka"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"146",Health Insurance - Sandra Emmel,Health Insurance - Sandra Emmel,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""146"", ""Name"": ""Health Insurance - Sandra Emmel"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Health Insurance - Sandra Emmel"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"174",HSA - BDS,HSA - BDS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""174"", ""Name"": ""HSA - BDS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""HSA - BDS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"177",HSA - Helen Holoubek,HSA - Helen Holoubek,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""177"", ""Name"": ""HSA - Helen Holoubek"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""HSA - Helen Holoubek"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"172",HSA - KK,HSA - KK,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""172"", ""Name"": ""HSA - KK"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""HSA - KK"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"187",HSA - MP,HSA - MP,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""187"", ""Name"": ""HSA - MP"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""HSA - MP"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"36",Insurance,Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""36"", ""Name"": ""Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"265",Insurance - Office & Contents,Insurance - Office & Contents,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""265"", ""Name"": ""Insurance - Office & Contents"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:55:16-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:55:16-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Insurance - Office & Contents"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance - Office & Contents"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"100",Insurance Overpayment,Insurance Overpayment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""100"", ""Name"": ""Insurance Overpayment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance Overpayment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"92",Insurance-Malpractice,Insurance-Malpractice,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""92"", ""Name"": ""Insurance-Malpractice"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-12-10T10:23:58-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"354",Insurance - Malpractice - SS,Insurance-Malpractice:Insurance - Malpractice - SS,Expense,Insurance,Expense,true,USD,"{""Id"": ""354"", ""Name"": ""Insurance - Malpractice - SS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-12-10T10:23:58-08:00"", ""LastUpdatedTime"": ""2023-12-10T10:23:58-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Insurance"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance - Malpractice - SS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"164",Insurance-Malpractice - BDS,Insurance-Malpractice:Insurance-Malpractice - BDS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""164"", ""Name"": ""Insurance-Malpractice - BDS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:18:10-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - BDS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"245",Insurance-Malpractice - DG,Insurance-Malpractice:Insurance-Malpractice - DG,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""245"", ""Name"": ""Insurance-Malpractice - DG"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T15:28:40-08:00"", ""LastUpdatedTime"": ""2023-02-28T14:21:07-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - DG"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"189",Insurance-Malpractice - JC,Insurance-Malpractice:Insurance-Malpractice - JC,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""189"", ""Name"": ""Insurance-Malpractice - JC"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:21:25-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - JC"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"157",Insurance-Malpractice - KK,Insurance-Malpractice:Insurance-Malpractice - KK,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""157"", ""Name"": ""Insurance-Malpractice - KK"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:18:37-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - KK"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"156",Insurance-Malpractice - LG,Insurance-Malpractice:Insurance-Malpractice - LG,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""156"", ""Name"": ""Insurance-Malpractice - LG"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:19:30-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - LG"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"158",Insurance-Malpractice - MP,Insurance-Malpractice:Insurance-Malpractice - MP,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""158"", ""Name"": ""Insurance-Malpractice - MP"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:19:48-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - MP"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"196",Insurance-Malpractice - RS,Insurance-Malpractice:Insurance-Malpractice - RS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""196"", ""Name"": ""Insurance-Malpractice - RS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:21:51-08:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - RS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"339",Insurance-Malpractice - SS,Insurance-Malpractice:Insurance-Malpractice - SS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""339"", ""Name"": ""Insurance-Malpractice - SS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-08-04T07:01:56-07:00"", ""LastUpdatedTime"": ""2023-08-04T07:01:56-07:00""}, ""ParentRef"": {""value"": ""92""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Insurance-Malpractice:Insurance-Malpractice - SS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"72",Interest Expense,Interest Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""72"", ""Name"": ""Interest Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Interest Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"19",Interest Income,Interest Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""19"", ""Name"": ""Interest Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Interest Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"96",Internet & Web Page,Internet & Web Page,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""96"", ""Name"": ""Internet & Web Page"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Internet & Web Page"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"182",Inventory Asset,Inventory Asset,Other Current Asset,OtherCurrentAssets,Asset,true,USD,"{""Id"": ""182"", ""Name"": ""Inventory Asset"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""12100"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Costs of inventory purchased for resale"", ""AccountSubType"": ""OtherCurrentAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Inventory Asset"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"235",Inventory Asset-1,Inventory Asset-1,Other Current Asset,Inventory,Asset,true,USD,"{""Id"": ""235"", ""Name"": ""Inventory Asset-1"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-21T07:57:34-07:00"", ""LastUpdatedTime"": ""2022-04-21T07:57:34-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Inventory"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Inventory Asset-1"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"243",JC Distribution,JC Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""243"", ""Name"": ""JC Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:54:54-08:00"", ""LastUpdatedTime"": ""2025-02-08T15:27:19-08:00""}, ""SyncToken"": ""3"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""2022"", ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 65000, ""FullyQualifiedName"": ""JC Distribution"", ""CurrentBalanceWithSubAccounts"": 65000}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"261",Journals & Publications,Journals & Publications,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""261"", ""Name"": ""Journals & Publications"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:43:30-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:43:30-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Journals & Publications"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Journals & Publications"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"239",KK Buyout,KK Buyout,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""239"", ""Name"": ""KK Buyout"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:43:05-08:00"", ""LastUpdatedTime"": ""2023-01-08T14:43:05-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""not sure how to categorize?"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""KK Buyout"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"58",Lab & X-Ray,Lab & X-Ray,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""58"", ""Name"": ""Lab & X-Ray"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Lab & X-Ray"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"262",Laboratory Fees,Laboratory Fees,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""262"", ""Name"": ""Laboratory Fees"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:46:57-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:46:57-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Laboratory Fees"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Laboratory Fees"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"79",Leasehold Improvements,Leasehold Improvements,Fixed Asset,OtherFixedAssets,Asset,true,USD,"{""Id"": ""79"", ""Name"": ""Leasehold Improvements"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2025-04-09T08:02:27-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Fixed Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherFixedAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": 75909.85, ""FullyQualifiedName"": ""Leasehold Improvements"", ""CurrentBalanceWithSubAccounts"": 75909.85}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"29",Legal & Accounting,Legal & Accounting,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""29"", ""Name"": ""Legal & Accounting"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Legal & Accounting"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"31",Legal & Accounting - Dr. George,Legal & Accounting - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""31"", ""Name"": ""Legal & Accounting - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Legal & Accounting - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"154",Legal & Accounting - Dr. Pierce,Legal & Accounting - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""154"", ""Name"": ""Legal & Accounting - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Legal & Accounting - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"30",Legal & Accounting- Dr. Kreilng,Legal & Accounting- Dr. Kreilng,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""30"", ""Name"": ""Legal & Accounting- Dr. Kreilng"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Legal & Accounting- Dr. Kreilng"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"240",LG Distribution,LG Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""240"", ""Name"": ""LG Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:46:13-08:00"", ""LastUpdatedTime"": ""2025-06-11T08:23:55-07:00""}, ""SyncToken"": ""4"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""2022 distribution"", ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""LG Distribution"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"130",Liability Insurance,Liability Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""130"", ""Name"": ""Liability Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Liability Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"263",Licenses & Registrations,Licenses & Registrations,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""263"", ""Name"": ""Licenses & Registrations"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:48:41-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:48:41-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Licenses & Registrations"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Licenses & Registrations"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"44",Life Insurance,Life Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""44"", ""Name"": ""Life Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"46",Life Insurance - Dr. George,Life Insurance - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""46"", ""Name"": ""Life Insurance - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2023-02-23T13:38:48-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"45",Life Insurance - Dr. Kreiling,Life Insurance - Dr. Kreiling,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""45"", ""Name"": ""Life Insurance - Dr. Kreiling"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance - Dr. Kreiling"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"159",Life Insurance - Dr. Pierce,Life Insurance - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""159"", ""Name"": ""Life Insurance - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"186",Life Insurance - Dr. Scherer,Life Insurance - Dr. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""186"", ""Name"": ""Life Insurance - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"224",Life Insurance - Dr. Shah,Life Insurance - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""224"", ""Name"": ""Life Insurance - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Protective Life insurance Company"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Life Insurance - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"122",Loan principal-capital,Loan principal-capital,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""122"", ""Name"": ""Loan principal-capital"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Loan principal-capital"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"126",Loan repayment-shareholder,Loan repayment-shareholder,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""126"", ""Name"": ""Loan repayment-shareholder"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Loan repayment-shareholder"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"118",Maintenance,Maintenance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""118"", ""Name"": ""Maintenance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Maintenance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"228",Markup,Markup,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""228"", ""Name"": ""Markup"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:29:43-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:29:43-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Markup"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"286",Meals & Entertainment,Meals & Entertainment,Expense,EntertainmentMeals,Expense,true,USD,"{""Id"": ""286"", ""Name"": ""Meals & Entertainment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T13:53:23-08:00"", ""LastUpdatedTime"": ""2023-02-27T13:53:23-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Meals & Entertainment"", ""AccountSubType"": ""EntertainmentMeals"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Meals & Entertainment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"68",Med Reimb. - Dr. G,Med Reimb. - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""68"", ""Name"": ""Med Reimb. - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Med Reimb. - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"67",Med Reimb. - Dr. K,Med Reimb. - Dr. K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""67"", ""Name"": ""Med Reimb. - Dr. K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Med Reimb. - Dr. K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"66",Med Reimbursement,Med Reimbursement,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""66"", ""Name"": ""Med Reimbursement"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Med Reimbursement"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"167",Med. Reimb. - MP,Med. Reimb. - MP,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""167"", ""Name"": ""Med. Reimb. - MP"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Med. Reimb. - MP"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"97",Medical Books,Medical Books,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""97"", ""Name"": ""Medical Books"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Books"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"88",Medical Equipment,Medical Equipment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""88"", ""Name"": ""Medical Equipment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Equipment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"171",Medical Equipment - BDS,Medical Equipment - BDS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""171"", ""Name"": ""Medical Equipment - BDS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Equipment - BDS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"166",Medical Equipment - LG,Medical Equipment - LG,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""166"", ""Name"": ""Medical Equipment - LG"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Equipment - LG"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"165",Medical Equipment - MP,Medical Equipment - MP,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""165"", ""Name"": ""Medical Equipment - MP"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Equipment - MP"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"10",Medical Supplies,Medical Supplies,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""10"", ""Name"": ""Medical Supplies"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:39-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Supplies"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"109",Medical Waste Removal,Medical Waste Removal,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""109"", ""Name"": ""Medical Waste Removal"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:52:00-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Medical Waste Removal"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Medical Waste Removal"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"264",Meetings & Seminars,Meetings & Seminars,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""264"", ""Name"": ""Meetings & Seminars"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T07:53:23-08:00"", ""LastUpdatedTime"": ""2023-02-24T07:53:23-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Meetings & Seminars"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Meetings & Seminars"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"288","Mgmt, Tax & Acctg","Mgmt, Tax & Acctg",Expense,LegalProfessionalFees,Expense,true,USD,"{""Id"": ""288"", ""Name"": ""Mgmt, Tax & Acctg"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T09:46:37-08:00"", ""LastUpdatedTime"": ""2023-03-01T09:46:37-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Mgmt, Tax & Acctg"", ""AccountSubType"": ""LegalProfessionalFees"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Mgmt, Tax & Acctg"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"373",Money Market 4106,Money Market 4106,Bank,MoneyMarket,Asset,true,USD,"{""Id"": ""373"", ""Name"": ""Money Market 4106"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2025-02-25T10:40:22-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:56:39-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Bank"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""MoneyMarket"", ""Classification"": ""Asset"", ""CurrentBalance"": 154056.93, ""FullyQualifiedName"": ""Money Market 4106"", ""CurrentBalanceWithSubAccounts"": 154056.93}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"244",MP Distribution,MP Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""244"", ""Name"": ""MP Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:55:26-08:00"", ""LastUpdatedTime"": ""2025-02-08T15:27:19-08:00""}, ""SyncToken"": ""4"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""2022"", ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 65000, ""FullyQualifiedName"": ""MP Distribution"", ""CurrentBalanceWithSubAccounts"": 65000}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"83",Notes Payable,Notes Payable,Long Term Liability,OtherLongTermLiabilities,Liability,true,USD,"{""Id"": ""83"", ""Name"": ""Notes Payable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Long Term Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherLongTermLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Notes Payable"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"99",Office Buildout Expense,Office Buildout Expense,Fixed Asset,Buildings,Asset,true,USD,"{""Id"": ""99"", ""Name"": ""Office Buildout Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2025-02-26T14:13:42-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Fixed Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Buildings"", ""Classification"": ""Asset"", ""CurrentBalance"": 762141.4, ""FullyQualifiedName"": ""Office Buildout Expense"", ""CurrentBalanceWithSubAccounts"": 762141.4}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"93",Office Equipment,Office Equipment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""93"", ""Name"": ""Office Equipment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:52:51-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Office Equipment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"27",Office Expenses,Office Expenses,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""27"", ""Name"": ""Office Expenses"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2024-08-21T11:02:09-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Office Expenses"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"91",Office Furniture,Office Furniture,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""91"", ""Name"": ""Office Furniture"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Office Furniture"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"8",Opening Bal Equity,Opening Bal Equity,Equity,OpeningBalanceEquity,Equity,true,USD,"{""Id"": ""8"", ""Name"": ""Opening Bal Equity"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""3000"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:15-07:00"", ""LastUpdatedTime"": ""2025-03-18T09:02:43-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OpeningBalanceEquity"", ""Classification"": ""Equity"", ""CurrentBalance"": -75295.14, ""FullyQualifiedName"": ""Opening Bal Equity"", ""CurrentBalanceWithSubAccounts"": -75295.14}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"15",Other Taxable,Other Taxable,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""15"", ""Name"": ""Other Taxable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Other Taxable"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"95",Pagers,Pagers,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""95"", ""Name"": ""Pagers"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Pagers"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"204",Patient Notification Tools,Patient Notification Tools,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""204"", ""Name"": ""Patient Notification Tools"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2023-04-25T10:30:03-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Patient Notification Tools"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"225",Payables account 3609,Payables account 3609,Bank,Checking,Asset,true,USD,"{""Id"": ""225"", ""Name"": ""Payables account 3609"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:55:03-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Bank"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Checking"", ""Classification"": ""Asset"", ""CurrentBalance"": 33070.3, ""FullyQualifiedName"": ""Payables account 3609"", ""CurrentBalanceWithSubAccounts"": 33070.3}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"6",Payroll Expenses,Payroll Expenses,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""6"", ""Name"": ""Payroll Expenses"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""6560"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:15-07:00"", ""LastUpdatedTime"": ""2024-02-13T12:40:27-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"279","401K Expense",Payroll Expenses:401K Expense,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""279"", ""Name"": ""401K Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T10:39:36-08:00"", ""LastUpdatedTime"": ""2024-02-13T12:39:02-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K Expense"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"363","401K - Associate Dr. George",Payroll Expenses:401K Expense:401K - Associate Dr. George,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""363"", ""Name"": ""401K - Associate Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:39:02-08:00"", ""LastUpdatedTime"": ""2024-02-13T12:39:02-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Associate Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"302","401K - Associate Dr. Ghosh",Payroll Expenses:401K Expense:401K - Associate Dr. Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""302"", ""Name"": ""401K - Associate Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:48:43-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:48:43-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Associate Dr. Ghosh"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Associate Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"297","401K - Dr. Canner",Payroll Expenses:401K Expense:401K - Dr. Canner,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""297"", ""Name"": ""401K - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:45:34-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:46:08-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Canner"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"300","401K - Dr. George",Payroll Expenses:401K Expense:401K - Dr. George,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""300"", ""Name"": ""401K - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:47:41-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:47:41-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. George"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"298","401K - Dr. Pierce",Payroll Expenses:401K Expense:401K - Dr. Pierce,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""298"", ""Name"": ""401K - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:46:44-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:46:44-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Pierce"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"299","401K - Dr. Scherer",Payroll Expenses:401K Expense:401K - Dr. Scherer,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""299"", ""Name"": ""401K - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:47:10-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:47:10-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Scherer"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"301","401K - Dr. Shah",Payroll Expenses:401K Expense:401K - Dr. Shah,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""301"", ""Name"": ""401K - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:48:06-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:48:06-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Dr. Shah"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"290","401K - Office Staff",Payroll Expenses:401K Expense:401K - Office Staff,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""290"", ""Name"": ""401K - Office Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-01T11:40:43-08:00"", ""LastUpdatedTime"": ""2023-03-01T11:46:19-08:00""}, ""ParentRef"": {""value"": ""279""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""401K - Office Staff"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:401K Expense:401K - Office Staff"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"361",Payroll Tax - Associate Dr. Shaikh,Payroll Expenses:Payroll Tax - Associate Dr. Shaikh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""361"", ""Name"": ""Payroll Tax - Associate Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:13:31-08:00"", ""LastUpdatedTime"": ""2024-02-13T14:52:13-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Associate Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"364",Payroll Tax - Associates Dr. George,Payroll Expenses:Payroll Tax - Associates Dr. George,Expense,PayrollTaxExpenses,Expense,true,USD,"{""Id"": ""364"", ""Name"": ""Payroll Tax - Associates Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:40:27-08:00"", ""LastUpdatedTime"": ""2024-02-13T12:40:27-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollTaxExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Associates Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"280",Payroll Tax - Associates Dr. Ghosh,Payroll Expenses:Payroll Tax - Associates Dr. Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""280"", ""Name"": ""Payroll Tax - Associates Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:16:57-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:16:57-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Associates Dr. Ghosh"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Associates Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"281",Payroll Tax - Dr. Canner,Payroll Expenses:Payroll Tax - Dr. Canner,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""281"", ""Name"": ""Payroll Tax - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:19:36-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:19:36-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Canner"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"282",Payroll Tax - Dr. George,Payroll Expenses:Payroll Tax - Dr. George,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""282"", ""Name"": ""Payroll Tax - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:19:58-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:19:58-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"358",Payroll Tax - Dr. Ghosh,Payroll Expenses:Payroll Tax - Dr. Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""358"", ""Name"": ""Payroll Tax - Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-09T08:39:36-08:00"", ""LastUpdatedTime"": ""2024-02-09T08:39:36-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"283",Payroll Tax - Dr. Pierce,Payroll Expenses:Payroll Tax - Dr. Pierce,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""283"", ""Name"": ""Payroll Tax - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:20:21-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:20:21-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Pierce"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"284",Payroll Tax - Dr. Scherer,Payroll Expenses:Payroll Tax - Dr. Scherer,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""284"", ""Name"": ""Payroll Tax - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:20:46-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:20:46-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Scherer"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"285",Payroll Tax - Dr. Shah,Payroll Expenses:Payroll Tax - Dr. Shah,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""285"", ""Name"": ""Payroll Tax - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-27T11:21:41-08:00"", ""LastUpdatedTime"": ""2023-02-27T11:21:41-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Shah"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"111",Payroll Tax - Office Staff,Payroll Expenses:Payroll Tax - Office Staff,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""111"", ""Name"": ""Payroll Tax - Office Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-02-27T11:17:22-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""5"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Office Staff"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Payroll Tax - Office Staff"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"16",Salaries,Payroll Expenses:Salaries,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""16"", ""Name"": ""Salaries"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2024-02-13T14:45:14-08:00""}, ""ParentRef"": {""value"": ""6""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"362",Payroll - Associate Dr. Shaikh,Payroll Expenses:Salaries:Payroll - Associate Dr. Shaikh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""362"", ""Name"": ""Payroll - Associate Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:19:53-08:00"", ""LastUpdatedTime"": ""2024-02-13T14:52:26-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Payroll - Associate Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"268",Payroll - Associates Dr. Ghosh,Payroll Expenses:Salaries:Payroll - Associates Dr. Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""268"", ""Name"": ""Payroll - Associates Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:41:04-08:00"", ""LastUpdatedTime"": ""2023-03-27T10:31:13-07:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll - Associates Dr. Ghosh"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Payroll - Associates Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"269",Payroll - Office Staff,Payroll Expenses:Salaries:Payroll - Office Staff,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""269"", ""Name"": ""Payroll - Office Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:41:36-08:00"", ""LastUpdatedTime"": ""2023-03-27T10:32:55-07:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll - Office Staff"", ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Payroll - Office Staff"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"369",Salary - Associate Dr. Shaikh,Payroll Expenses:Salaries:Salary - Associate Dr. Shaikh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""369"", ""Name"": ""Salary - Associate Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T14:45:14-08:00"", ""LastUpdatedTime"": ""2024-02-13T14:50:44-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Associate Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"366",Salary - Associates Dr. George,Payroll Expenses:Salaries:Salary - Associates Dr. George,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""366"", ""Name"": ""Salary - Associates Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:44:50-08:00"", ""LastUpdatedTime"": ""2024-02-13T12:49:52-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Associates Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"247",Salary - Dr. Canner,Payroll Expenses:Salaries:Salary - Dr. Canner,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""247"", ""Name"": ""Salary - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T10:41:54-08:00"", ""LastUpdatedTime"": ""2024-02-13T14:45:32-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"250",Salary - Dr. George,Payroll Expenses:Salaries:Salary - Dr. George,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""250"", ""Name"": ""Salary - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T10:44:09-08:00"", ""LastUpdatedTime"": ""2023-02-24T09:48:44-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"359",Salary - Dr. Ghosh,Payroll Expenses:Salaries:Salary - Dr. Ghosh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""359"", ""Name"": ""Salary - Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-09T08:48:23-08:00"", ""LastUpdatedTime"": ""2024-02-09T08:48:23-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"248",Salary - Dr. Pierce,Payroll Expenses:Salaries:Salary - Dr. Pierce,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""248"", ""Name"": ""Salary - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T10:43:05-08:00"", ""LastUpdatedTime"": ""2023-02-24T09:48:44-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"249",Salary - Dr. Scherer,Payroll Expenses:Salaries:Salary - Dr. Scherer,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""249"", ""Name"": ""Salary - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T10:43:35-08:00"", ""LastUpdatedTime"": ""2023-02-24T09:48:44-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"246",Salary - Dr. Shah,Payroll Expenses:Salaries:Salary - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""246"", ""Name"": ""Salary - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T10:40:19-08:00"", ""LastUpdatedTime"": ""2023-02-24T09:48:44-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"352",Salary - Dr. Shaikh,Payroll Expenses:Salaries:Salary - Dr. Shaikh,Expense,PayrollExpenses,Expense,true,USD,"{""Id"": ""352"", ""Name"": ""Salary - Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-21T11:05:48-08:00"", ""LastUpdatedTime"": ""2023-11-21T11:05:48-08:00""}, ""ParentRef"": {""value"": ""16""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollExpenses"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses:Salaries:Salary - Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"112",Payroll Expenses - direct dep,Payroll Expenses - direct dep,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""112"", ""Name"": ""Payroll Expenses - direct dep"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses - direct dep"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"113",Payroll Expenses - readychex,Payroll Expenses - readychex,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""113"", ""Name"": ""Payroll Expenses - readychex"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Expenses - readychex"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"7",Payroll Liabilities,Payroll Liabilities,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""7"", ""Name"": ""Payroll Liabilities"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""2100"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:15-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities"", ""CurrentBalanceWithSubAccounts"": -462.95}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"336",BCBS Health,Payroll Liabilities:BCBS Health,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""336"", ""Name"": ""BCBS Health"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-21T11:06:20-07:00"", ""LastUpdatedTime"": ""2025-08-28T07:15:33-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""BCBS Health"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -419.2, ""FullyQualifiedName"": ""Payroll Liabilities:BCBS Health"", ""CurrentBalanceWithSubAccounts"": -419.2}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"357",Payroll Liabilities,Payroll Liabilities:Payroll Liabilities,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""357"", ""Name"": ""Payroll Liabilities"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-09T08:36:34-08:00"", ""LastUpdatedTime"": ""2025-08-06T05:52:39-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Ghosh"", ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Liabilities"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"353",Payroll Tax - Associate Dr. Shaikh,Payroll Liabilities:Payroll Tax - Associate Dr. Shaikh,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""353"", ""Name"": ""Payroll Tax - Associate Dr. Shaikh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-21T11:08:27-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0.01, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Associate Dr. Shaikh"", ""CurrentBalanceWithSubAccounts"": 0.01}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"365",Payroll Tax - Associates Dr. George,Payroll Liabilities:Payroll Tax - Associates Dr. George,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""365"", ""Name"": ""Payroll Tax - Associates Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-13T12:42:14-08:00"", ""LastUpdatedTime"": ""2025-06-09T12:58:54-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Associates Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"271",Payroll Tax - Dr. Canner,Payroll Liabilities:Payroll Tax - Dr. Canner,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""271"", ""Name"": ""Payroll Tax - Dr. Canner"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:45:18-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Canner"", ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": -440.38, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. Canner"", ""CurrentBalanceWithSubAccounts"": -440.38}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"272",Payroll Tax - Dr. George,Payroll Liabilities:Payroll Tax - Dr. George,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""272"", ""Name"": ""Payroll Tax - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:48:02-08:00"", ""LastUpdatedTime"": ""2025-07-02T02:52:20-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""5"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. George"", ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"360",Payroll Tax - Dr. Ghosh,Payroll Liabilities:Payroll Tax - Dr. Ghosh,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""360"", ""Name"": ""Payroll Tax - Dr. Ghosh"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-02-09T09:05:12-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. Ghosh"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"273",Payroll Tax - Dr. Pierce,Payroll Liabilities:Payroll Tax - Dr. Pierce,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""273"", ""Name"": ""Payroll Tax - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:48:38-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Pierce"", ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"274",Payroll Tax - Dr. Scherer,Payroll Liabilities:Payroll Tax - Dr. Scherer,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""274"", ""Name"": ""Payroll Tax - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:49:07-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Dr. Scherer"", ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": -1446.54, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": -1446.54}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"275",Payroll Tax - Dr. Shah,Payroll Liabilities:Payroll Tax - Dr. Shah,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""275"", ""Name"": ""Payroll Tax - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:50:03-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"276",Payroll Tax - Office Staff,Payroll Liabilities:Payroll Tax - Office Staff,Other Current Liability,PayrollTaxPayable,Liability,true,USD,"{""Id"": ""276"", ""Name"": ""Payroll Tax - Office Staff"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:50:39-08:00"", ""LastUpdatedTime"": ""2025-09-03T05:13:58-07:00""}, ""ParentRef"": {""value"": ""7""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Payroll Tax - Office Staff"", ""AccountSubType"": ""PayrollTaxPayable"", ""Classification"": ""Liability"", ""CurrentBalance"": 1843.16, ""FullyQualifiedName"": ""Payroll Liabilities:Payroll Tax - Office Staff"", ""CurrentBalanceWithSubAccounts"": 1843.16}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"115",Payroll Service,Payroll Service,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""115"", ""Name"": ""Payroll Service"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2023-02-23T13:28:12-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Service"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"256",Employee Healthcare Insurance,Payroll Service:Employee Healthcare Insurance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""256"", ""Name"": ""Employee Healthcare Insurance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:28:12-08:00"", ""LastUpdatedTime"": ""2023-02-23T13:28:12-08:00""}, ""ParentRef"": {""value"": ""115""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Employee Healthcare Insurance"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Payroll Service:Employee Healthcare Insurance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"152",Personal Expense,Personal Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""152"", ""Name"": ""Personal Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Personal Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"193",Personal Expense - BDS,Personal Expense - BDS,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""193"", ""Name"": ""Personal Expense - BDS"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Personal Expense - BDS"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"129",Personal Expense - KK,Personal Expense - KK,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""129"", ""Name"": ""Personal Expense - KK"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Personal Expense - KK"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"128",Personal Expense - LG,Personal Expense - LG,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""128"", ""Name"": ""Personal Expense - LG"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Personal Expense - LG"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"155",Personal Expense - MP,Personal Expense - MP,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""155"", ""Name"": ""Personal Expense - MP"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Personal Expense - MP"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"188",PNC Checking 6095 Stripe Account,PNC Checking 6095 Stripe Account,Bank,Checking,Asset,true,USD,"{""Id"": ""188"", ""Name"": ""PNC Checking 6095 Stripe Account"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2025-09-04T00:31:09-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Bank"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Checking"", ""Classification"": ""Asset"", ""CurrentBalance"": 3303.22, ""FullyQualifiedName"": ""PNC Checking 6095 Stripe Account"", ""CurrentBalanceWithSubAccounts"": 3303.22}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"236",PNC credit card #0056,PNC credit card #0056,Credit Card,CreditCard,Liability,true,USD,"{""Id"": ""236"", ""Name"": ""PNC credit card #0056"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-05-24T11:31:03-07:00"", ""LastUpdatedTime"": ""2025-08-29T06:12:51-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Credit Card"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""CC all users"", ""AccountSubType"": ""CreditCard"", ""Classification"": ""Liability"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""PNC credit card #0056"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"195",PNC Online Checking 3242,PNC Online Checking 3242,Bank,Checking,Asset,true,USD,"{""Id"": ""195"", ""Name"": ""PNC Online Checking 3242"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2025-09-03T05:16:46-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Bank"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Checking"", ""Classification"": ""Asset"", ""CurrentBalance"": 131100.13, ""FullyQualifiedName"": ""PNC Online Checking 3242"", ""CurrentBalanceWithSubAccounts"": 131100.13}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"32",Postage & Delivery,Postage & Delivery,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""32"", ""Name"": ""Postage & Delivery"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:57:36-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Postage & Delivery"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Postage & Delivery"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"337",PPP 2,PPP 2,Other Income,OtherMiscellaneousIncome,Revenue,true,USD,"{""Id"": ""337"", ""Name"": ""PPP 2"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-05-04T12:14:22-07:00"", ""LastUpdatedTime"": ""2023-05-04T12:14:22-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""PPP 2"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"9",Practice Income,Practice Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""9"", ""Name"": ""Practice Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:15-07:00"", ""LastUpdatedTime"": ""2023-11-14T08:31:52-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"234",DIT Adjustment,Practice Income:DIT Adjustment,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""234"", ""Name"": ""DIT Adjustment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-21T07:57:34-07:00"", ""LastUpdatedTime"": ""2023-02-28T14:14:17-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""3"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""DIT Adjustment"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:DIT Adjustment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"251",Dr. Canner - Income,Practice Income:Dr. Canner - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""251"", ""Name"": ""Dr. Canner - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:02:55-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:27:42-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""3"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Canner - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Canner - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"308",Cash/Co-pay Income,Practice Income:Dr. Canner - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""308"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:26:38-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:26:59-08:00""}, ""ParentRef"": {""value"": ""251""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Canner - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"307",Hospital Income,Practice Income:Dr. Canner - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""307"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:26:11-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:50:41-08:00""}, ""ParentRef"": {""value"": ""251""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Canner - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"310",Office Income,Practice Income:Dr. Canner - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""310"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:27:42-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:27:42-08:00""}, ""ParentRef"": {""value"": ""251""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Canner - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"309",Telemed Income,Practice Income:Dr. Canner - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""309"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:27:25-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:27:25-08:00""}, ""ParentRef"": {""value"": ""251""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Canner - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"315",Dr. George - Income,Practice Income:Dr. George - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""315"", ""Name"": ""Dr. George - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:46:03-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:51:21-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. George - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. George - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"318",Cash/Co-pay Income,Practice Income:Dr. George - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""318"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:48:07-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:48:07-08:00""}, ""ParentRef"": {""value"": ""315""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. George - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"319",Hospital  Income,Practice Income:Dr. George - Income:Hospital  Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""319"", ""Name"": ""Hospital  Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:48:33-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:48:33-08:00""}, ""ParentRef"": {""value"": ""315""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital  Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. George - Income:Hospital  Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"322",Office Income,Practice Income:Dr. George - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""322"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:51:21-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:51:21-08:00""}, ""ParentRef"": {""value"": ""315""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. George - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"321",Telemed Income,Practice Income:Dr. George - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""321"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:49:39-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:49:39-08:00""}, ""ParentRef"": {""value"": ""315""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. George - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"259",Dr. Ghosh - Income,Practice Income:Dr. Ghosh - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""259"", ""Name"": ""Dr. Ghosh - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T15:18:18-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:04:10-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Ghosh - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Ghosh - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"333",Cash/Co-pay Income,Practice Income:Dr. Ghosh - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""333"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T11:03:10-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:03:10-08:00""}, ""ParentRef"": {""value"": ""259""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Ghosh - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"332",Hospital Income,Practice Income:Dr. Ghosh - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""332"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T11:02:45-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:02:45-08:00""}, ""ParentRef"": {""value"": ""259""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Ghosh - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"334",Office Income,Practice Income:Dr. Ghosh - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""334"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T11:03:39-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:03:39-08:00""}, ""ParentRef"": {""value"": ""259""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Ghosh - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"335",Telemed Income,Practice Income:Dr. Ghosh - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""335"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T11:04:10-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:04:10-08:00""}, ""ParentRef"": {""value"": ""259""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Ghosh - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"316",Dr. Pierce - Income,Practice Income:Dr. Pierce - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""316"", ""Name"": ""Dr. Pierce - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:46:49-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:53:42-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Pierce - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Pierce - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"325",Cash/Co-pay Income,Practice Income:Dr. Pierce - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""325"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:53:13-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:53:42-08:00""}, ""ParentRef"": {""value"": ""316""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Pierce - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"323",Hospital Income,Practice Income:Dr. Pierce - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""323"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:52:09-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:52:09-08:00""}, ""ParentRef"": {""value"": ""316""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Pierce - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"320",Office Income,Practice Income:Dr. Pierce - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""320"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:48:53-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:50:41-08:00""}, ""ParentRef"": {""value"": ""316""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Pierce - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"324",Telemed Income,Practice Income:Dr. Pierce - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""324"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:52:40-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:52:40-08:00""}, ""ParentRef"": {""value"": ""316""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Pierce - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"317",Dr. Scherer - Income,Practice Income:Dr. Scherer - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""317"", ""Name"": ""Dr. Scherer - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:47:24-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:56:22-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Scherer - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Scherer - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"326",Cash/Co-pay Income,Practice Income:Dr. Scherer - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""326"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:54:59-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:54:59-08:00""}, ""ParentRef"": {""value"": ""317""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Scherer - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"327",Hospital Income,Practice Income:Dr. Scherer - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""327"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:55:17-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:55:17-08:00""}, ""ParentRef"": {""value"": ""317""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Scherer - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"328",Office Income,Practice Income:Dr. Scherer - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""328"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:55:42-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:55:42-08:00""}, ""ParentRef"": {""value"": ""317""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Scherer - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"329",Telemed Income,Practice Income:Dr. Scherer - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""329"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:56:22-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:56:22-08:00""}, ""ParentRef"": {""value"": ""317""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Scherer - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"255",Dr. Shah - Income,Practice Income:Dr. Shah - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""255"", ""Name"": ""Dr. Shah - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:05:56-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:43:16-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""4"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Shah Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shah - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"311",Cash/Co-pay Income,Practice Income:Dr. Shah - Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""311"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:36:09-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:43:16-08:00""}, ""ParentRef"": {""value"": ""255""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shah - Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"312",Hospital Income,Practice Income:Dr. Shah - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""312"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:38:24-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:43:16-08:00""}, ""ParentRef"": {""value"": ""255""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shah - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"314",Office Income,Practice Income:Dr. Shah - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""314"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:39:56-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:43:16-08:00""}, ""ParentRef"": {""value"": ""255""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shah - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"313",Telemed Income,Practice Income:Dr. Shah - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""313"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:39:11-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:43:16-08:00""}, ""ParentRef"": {""value"": ""255""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shah - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"347",Dr. Shaikh - Income,Practice Income:Dr. Shaikh - Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""347"", ""Name"": ""Dr. Shaikh - Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-09T10:45:45-08:00"", ""LastUpdatedTime"": ""2023-11-21T09:21:17-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Dr. Shaikh - Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shaikh - Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"349",Hospital Income,Practice Income:Dr. Shaikh - Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""349"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-09T10:47:10-08:00"", ""LastUpdatedTime"": ""2023-11-09T10:47:10-08:00""}, ""ParentRef"": {""value"": ""347""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shaikh - Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"348",Office Income,Practice Income:Dr. Shaikh - Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""348"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-09T10:46:48-08:00"", ""LastUpdatedTime"": ""2023-11-09T10:46:48-08:00""}, ""ParentRef"": {""value"": ""347""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shaikh - Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"351",Telemed Income,Practice Income:Dr. Shaikh - Income:Telemed Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""351"", ""Name"": ""Telemed Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-11-13T18:03:56-08:00"", ""LastUpdatedTime"": ""2023-11-14T08:32:53-08:00""}, ""ParentRef"": {""value"": ""347""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Dr. Shaikh - Income:Telemed Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"258",Flu Shot Income,Practice Income:Flu Shot Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""258"", ""Name"": ""Flu Shot Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T15:05:53-08:00"", ""LastUpdatedTime"": ""2023-02-23T15:05:53-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Flu Shot Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Flu Shot Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"305",Hospital Income,Practice Income:Hospital Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""305"", ""Name"": ""Hospital Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T08:39:07-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:21:39-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Hospital Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Hospital Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"303",Office - Staff Income,Practice Income:Office - Staff Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""303"", ""Name"": ""Office - Staff Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T08:38:15-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:00:14-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office - Staff Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Office - Staff Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"331",Cash/Co-pay Income,Practice Income:Office - Staff Income:Cash/Co-pay Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""331"", ""Name"": ""Cash/Co-pay Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T11:00:14-08:00"", ""LastUpdatedTime"": ""2023-03-11T11:00:14-08:00""}, ""ParentRef"": {""value"": ""303""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Cash/Co-pay Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Office - Staff Income:Cash/Co-pay Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"330",Office Income,Practice Income:Office - Staff Income:Office Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""330"", ""Name"": ""Office Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T10:59:45-08:00"", ""LastUpdatedTime"": ""2023-03-11T10:59:45-08:00""}, ""ParentRef"": {""value"": ""303""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Office Income"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Office - Staff Income:Office Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"287",Other Income,Practice Income:Other Income,Income,OtherPrimaryIncome,Revenue,true,USD,"{""Id"": ""287"", ""Name"": ""Other Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-28T14:27:47-08:00"", ""LastUpdatedTime"": ""2023-02-28T14:27:47-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherPrimaryIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Other Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"252",Professional Receipts - Dr. George,Practice Income:Professional Receipts - Dr. George,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""252"", ""Name"": ""Professional Receipts - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:04:49-08:00"", ""LastUpdatedTime"": ""2023-02-23T13:31:26-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Professional Receipts - Dr. George"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Professional Receipts - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"253",Professional Receipts - Dr. Pierce,Practice Income:Professional Receipts - Dr. Pierce,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""253"", ""Name"": ""Professional Receipts - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:05:18-08:00"", ""LastUpdatedTime"": ""2023-02-23T13:31:40-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""2"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Professional Receipts - Dr. Pierce"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Professional Receipts - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"254",Professional Receipts - Scherer,Practice Income:Professional Receipts - Scherer,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""254"", ""Name"": ""Professional Receipts - Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T13:05:40-08:00"", ""LastUpdatedTime"": ""2023-02-23T13:30:08-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""1"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Professional Receipts - Scherer"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Professional Receipts - Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"304",Telemed,Practice Income:Telemed,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""304"", ""Name"": ""Telemed"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-03-11T08:38:37-08:00"", ""LastUpdatedTime"": ""2023-03-11T08:38:37-08:00""}, ""ParentRef"": {""value"": ""9""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Telemed"", ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Income:Telemed"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"180",Practice Mgmt Software Lease,Practice Mgmt Software Lease,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""180"", ""Name"": ""Practice Mgmt Software Lease"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Practice Mgmt Software Lease"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"219",PRF,PRF,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""219"", ""Name"": ""PRF"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""PRF"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"89",Printing & Reproduction,Printing & Reproduction,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""89"", ""Name"": ""Printing & Reproduction"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2023-02-24T07:58:26-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Printing & Reproduction"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Printing & Reproduction"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"63",Prof Meetings,Prof Meetings,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""63"", ""Name"": ""Prof Meetings"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Prof Meetings"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"65",Prof Meetings - Dr. G,Prof Meetings - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""65"", ""Name"": ""Prof Meetings - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Prof Meetings - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"64",Prof Meetings - Dr. K,Prof Meetings - Dr. K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""64"", ""Name"": ""Prof Meetings - Dr. K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Prof Meetings - Dr. K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"52",Professional Dues,Professional Dues,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""52"", ""Name"": ""Professional Dues"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2023-02-24T08:00:23-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Professional Dues"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Professional Dues"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"59",Professional Expense,Professional Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""59"", ""Name"": ""Professional Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Professional Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"73",Profit Sharing,Profit Sharing,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""73"", ""Name"": ""Profit Sharing"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Profit Sharing"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"125",Profit Sharing Account,Profit Sharing Account,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""125"", ""Name"": ""Profit Sharing Account"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Profit Sharing Account"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"82",Profit Sharing Payable,Profit Sharing Payable,Other Current Liability,OtherCurrentLiabilities,Liability,true,USD,"{""Id"": ""82"", ""Name"": ""Profit Sharing Payable"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2025-09-01T06:59:49-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Liability"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentLiabilities"", ""Classification"": ""Liability"", ""CurrentBalance"": -123375.32, ""FullyQualifiedName"": ""Profit Sharing Payable"", ""CurrentBalanceWithSubAccounts"": -123375.32}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"60",Promotion,Promotion,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""60"", ""Name"": ""Promotion"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Promotion"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"62",Promotion - Dr. G,Promotion - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""62"", ""Name"": ""Promotion - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Promotion - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"61",Promotion - Dr. K,Promotion - Dr. K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""61"", ""Name"": ""Promotion - Dr. K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Promotion - Dr. K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"233",Reconciliation Discrepancies,Reconciliation Discrepancies,Other Expense,OtherMiscellaneousExpense,Expense,true,USD,"{""Id"": ""233"", ""Name"": ""Reconciliation Discrepancies"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-19T13:53:56-07:00"", ""LastUpdatedTime"": ""2022-04-19T13:53:56-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousExpense"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Reconciliation Discrepancies"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"127",Refunds,Refunds,Expense,OtherBusinessExpenses,,true,USD,"{""Id"": ""127"", ""Name"": ""Refunds"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2024-08-21T11:02:35-07:00""}, ""SyncToken"": ""2"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherBusinessExpenses"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Refunds"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"131",Reimbursement,Reimbursement,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""131"", ""Name"": ""Reimbursement"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Reimbursement"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"12",Rent,Rent,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""12"", ""Name"": ""Rent"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:39-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Rent"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"71",Repairs & Maintenance,Repairs & Maintenance,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""71"", ""Name"": ""Repairs & Maintenance"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Repairs & Maintenance"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"87",Retained Earnings,Retained Earnings,Equity,RetainedEarnings,Equity,true,USD,"{""Id"": ""87"", ""Name"": ""Retained Earnings"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""3900"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:18-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""RetainedEarnings"", ""Classification"": ""Equity"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Retained Earnings"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"266",Retirement Plan Fees,Retirement Plan Fees,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""266"", ""Name"": ""Retirement Plan Fees"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T08:03:04-08:00"", ""LastUpdatedTime"": ""2023-02-24T08:03:04-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""Retirement Plan Fees"", ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Retirement Plan Fees"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"242",RS Distribution,RS Distribution,Equity,PartnerDistributions,,true,USD,"{""Id"": ""242"", ""Name"": ""RS Distribution"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-01-08T14:51:21-08:00"", ""LastUpdatedTime"": ""2025-02-08T15:27:19-08:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Equity"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""Description"": ""2022"", ""AccountSubType"": ""PartnerDistributions"", ""CurrentBalance"": 65000, ""FullyQualifiedName"": ""RS Distribution"", ""CurrentBalanceWithSubAccounts"": 65000}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"119",Salaries-Temps,Salaries-Temps,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""119"", ""Name"": ""Salaries-Temps"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Salaries-Temps"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"17",Salary - Dr K,Salary - Dr K,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""17"", ""Name"": ""Salary - Dr K"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Salary - Dr K"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"18",Salary - Dr. G,Salary - Dr. G,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""18"", ""Name"": ""Salary - Dr. G"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Salary - Dr. G"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"277",Sales of Product Income,Sales of Product Income,Income,SalesOfProductIncome,Revenue,true,USD,"{""Id"": ""277"", ""Name"": ""Sales of Product Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-24T09:24:51-08:00"", ""LastUpdatedTime"": ""2023-02-24T09:24:51-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""SalesOfProductIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Sales of Product Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"51",Sales Tax,Sales Tax,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""51"", ""Name"": ""Sales Tax"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Sales Tax"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"370",Security Deposits 75th St.,Security Deposits 75th St.,Other Asset,SecurityDeposits,Asset,true,USD,"{""Id"": ""370"", ""Name"": ""Security Deposits 75th St."", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2024-03-19T08:52:57-07:00"", ""LastUpdatedTime"": ""2024-04-02T10:02:13-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""SecurityDeposits"", ""Classification"": ""Asset"", ""CurrentBalance"": 12795.3, ""FullyQualifiedName"": ""Security Deposits 75th St."", ""CurrentBalanceWithSubAccounts"": 12795.3}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"221",Service fee,Service fee,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""221"", ""Name"": ""Service fee"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Service fee"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"226",Services,Services,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""226"", ""Name"": ""Services"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:09:23-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Services"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"162",Study Enrollment Reimbursement,Study Enrollment Reimbursement,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""162"", ""Name"": ""Study Enrollment Reimbursement"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Study Enrollment Reimbursement"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"47",Taxes,Taxes,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""47"", ""Name"": ""Taxes"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Taxes"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"50",Taxes - Corporate Income,Taxes - Corporate Income,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""50"", ""Name"": ""Taxes - Corporate Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Taxes - Corporate Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"222",Taxes - Real Estate,Taxes - Real Estate,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""222"", ""Name"": ""Taxes - Real Estate"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Taxes - Real Estate"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"49",Taxes - U/C Federal,Taxes - U/C Federal,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""49"", ""Name"": ""Taxes - U/C Federal"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Taxes - U/C Federal"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"48",Taxes - U/C State,Taxes - U/C State,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""48"", ""Name"": ""Taxes - U/C State"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Taxes - U/C State"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"11",Telephone,Telephone,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""11"", ""Name"": ""Telephone"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2023-09-14T08:02:37-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"344",Telephone - Dr. George,Telephone:Telephone - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""344"", ""Name"": ""Telephone - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-09-14T08:02:37-07:00"", ""LastUpdatedTime"": ""2023-09-14T08:02:37-07:00""}, ""ParentRef"": {""value"": ""11""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone:Telephone - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"342",Telephone - Dr. Pierce,Telephone:Telephone - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""342"", ""Name"": ""Telephone - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-09-14T08:01:01-07:00"", ""LastUpdatedTime"": ""2023-09-14T08:01:01-07:00""}, ""ParentRef"": {""value"": ""11""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone:Telephone - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"343",Telephone - Dr. Scherer,Telephone:Telephone - Dr. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""343"", ""Name"": ""Telephone - Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-09-14T08:01:57-07:00"", ""LastUpdatedTime"": ""2023-09-14T08:01:57-07:00""}, ""ParentRef"": {""value"": ""11""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone:Telephone - Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"341",Telephone - Dr. Shah,Telephone:Telephone - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""341"", ""Name"": ""Telephone - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-09-14T08:00:33-07:00"", ""LastUpdatedTime"": ""2023-09-14T08:00:33-07:00""}, ""ParentRef"": {""value"": ""11""}, ""SyncToken"": ""0"", ""SubAccount"": true, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone:Telephone - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"26",Telephone Answering Service,Telephone Answering Service,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""26"", ""Name"": ""Telephone Answering Service"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Answering Service"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"21",Telephone Cellular,Telephone Cellular,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""21"", ""Name"": ""Telephone Cellular"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"23",Telephone Cellular - Dr. George,Telephone Cellular - Dr. George,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""23"", ""Name"": ""Telephone Cellular - Dr. George"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular - Dr. George"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"138",Telephone Cellular - Dr. Pierce,Telephone Cellular - Dr. Pierce,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""138"", ""Name"": ""Telephone Cellular - Dr. Pierce"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular - Dr. Pierce"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"214",Telephone Cellular - Dr. Shah,Telephone Cellular - Dr. Shah,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""214"", ""Name"": ""Telephone Cellular - Dr. Shah"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular - Dr. Shah"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"22",Telephone Cellular- Dr. Kreilig,Telephone Cellular- Dr. Kreilig,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""22"", ""Name"": ""Telephone Cellular- Dr. Kreilig"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular- Dr. Kreilig"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"139",Telephone Cellular- Dr. Scherer,Telephone Cellular- Dr. Scherer,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""139"", ""Name"": ""Telephone Cellular- Dr. Scherer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:20-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Cellular- Dr. Scherer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"24",Telephone Equipment,Telephone Equipment,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""24"", ""Name"": ""Telephone Equipment"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Equipment"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"25",Telephone Office,Telephone Office,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""25"", ""Name"": ""Telephone Office"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Telephone Office"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"194",Transfer,Transfer,Bank,Checking,Asset,true,USD,"{""Id"": ""194"", ""Name"": ""Transfer"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:22-07:00"", ""LastUpdatedTime"": ""2024-08-21T10:52:57-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Bank"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""Checking"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Transfer"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"227",Unapplied Cash Bill Payment Expense,Unapplied Cash Bill Payment Expense,Expense,UnappliedCashBillPaymentExpense,Expense,true,USD,"{""Id"": ""227"", ""Name"": ""Unapplied Cash Bill Payment Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:24-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:09:24-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""UnappliedCashBillPaymentExpense"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Unapplied Cash Bill Payment Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"257",Unapplied Cash Payment Income,Unapplied Cash Payment Income,Income,UnappliedCashPaymentIncome,Revenue,true,USD,"{""Id"": ""257"", ""Name"": ""Unapplied Cash Payment Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2023-02-23T14:47:16-08:00"", ""LastUpdatedTime"": ""2023-02-23T14:47:16-08:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""UnappliedCashPaymentIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Unapplied Cash Payment Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"232",Uncategorized Asset,Uncategorized Asset,Other Current Asset,OtherCurrentAssets,Asset,true,USD,"{""Id"": ""232"", ""Name"": ""Uncategorized Asset"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:40:44-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:40:44-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Other Current Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherCurrentAssets"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Uncategorized Asset"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"229",Uncategorized Expense,Uncategorized Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""229"", ""Name"": ""Uncategorized Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:40:44-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:40:44-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Uncategorized Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"105",Uncategorized Expenses,Uncategorized Expenses,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""105"", ""Name"": ""Uncategorized Expenses"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""6999"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Uncategorized Expenses"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"231",Uncategorized Income,Uncategorized Income,Income,ServiceFeeIncome,Revenue,true,USD,"{""Id"": ""231"", ""Name"": ""Uncategorized Income"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:40:44-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:40:44-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Income"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""ServiceFeeIncome"", ""Classification"": ""Revenue"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Uncategorized Income"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"103",Undeposited Funds,Undeposited Funds,Other Current Asset,UndepositedFunds,Asset,true,USD,"{""Id"": ""103"", ""Name"": ""Undeposited Funds"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""AcctNum"": ""1499"", ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:19-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:09:19-07:00""}, ""SyncToken"": ""1"", ""SubAccount"": false, ""AccountType"": ""Other Current Asset"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""UndepositedFunds"", ""Classification"": ""Asset"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Undeposited Funds"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"153",Unemployment Taxes,Unemployment Taxes,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""153"", ""Name"": ""Unemployment Taxes"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:21-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Unemployment Taxes"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"13",Utilities,Utilities,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""13"", ""Name"": ""Utilities"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Utilities"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"20",Vaccines,Vaccines,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""20"", ""Name"": ""Vaccines"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:16-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Vaccines"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"205",Website Expense,Website Expense,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""205"", ""Name"": ""Website Expense"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:23-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:41-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Website Expense"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
"37",Workmans Comp Ins,Workmans Comp Ins,Expense,OtherMiscellaneousServiceCost,Expense,true,USD,"{""Id"": ""37"", ""Name"": ""Workmans Comp Ins"", ""Active"": true, ""domain"": ""QBO"", ""sparse"": false, ""MetaData"": {""CreateTime"": ""2022-04-15T12:09:17-07:00"", ""LastUpdatedTime"": ""2022-04-15T12:32:40-07:00""}, ""SyncToken"": ""0"", ""SubAccount"": false, ""AccountType"": ""Expense"", ""CurrencyRef"": {""name"": ""United States Dollar"", ""value"": ""USD""}, ""AccountSubType"": ""OtherMiscellaneousServiceCost"", ""Classification"": ""Expense"", ""CurrentBalance"": 0, ""FullyQualifiedName"": ""Workmans Comp Ins"", ""CurrentBalanceWithSubAccounts"": 0}",2025-09-04 16:50:11.204 +0530,2025-09-04 16:50:11.204 +0530
