"id","report_name","report_basis","start_date","end_date","currency","generated_at","raw","created_at"
10,CashFlow,,2025-06-01,2025-06-30,USD,2025-09-05 11:00:18.000 +0530,"{""Rows"": {""Row"": [{""Rows"": {""Row"": [{""type"": ""Data"", ""group"": ""NetIncome"", ""ColData"": [{""value"": ""Net Income""}, {""value"": ""22180.03""}]}, {""Rows"": {""Row"": [{""type"": ""Data"", ""ColData"": [{""id"": ""102"", ""value"": ""Accounts Receivable""}, {""value"": ""-1743.20""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""81"", ""value"": ""Accounts Payable""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Chase credit card #4012""}, {""value"": ""-6315.83""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""236"", ""value"": ""PNC credit card #0056""}, {""value"": ""-288.14""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""291"", ""value"": ""401K:401K - Dr. Canner""}, {""value"": ""-576.92""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""356"", ""value"": ""401K:401K - Dr. Ghosh""}, {""value"": ""-903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""295"", ""value"": ""401K:401K - Dr. Scherer""}, {""value"": ""-903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""296"", ""value"": ""401K:401K - Dr. Shah""}, {""value"": ""-903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""401K:401K - Dr. Shaikh""}, {""value"": ""-1236.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""289"", ""value"": ""401K:401K - Office Staff""}, {""value"": ""-1335.48""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""98"", ""value"": ""Bank Loan""}, {""value"": ""-4954.57""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Direct Deposit Liabilities""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Doctor Admin Income Payable""}, {""value"": ""3487.50""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""336"", ""value"": ""Payroll Liabilities:BCBS Health""}, {""value"": ""157.20""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""357"", ""value"": ""Payroll Liabilities:Payroll Liabilities""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""353"", ""value"": ""Payroll Liabilities:Payroll Tax - Associate Dr. Shaikh""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""271"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Canner""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""272"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. George""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""360"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Ghosh""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""273"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Pierce""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""274"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Scherer""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""275"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Shah""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""276"", ""value"": ""Payroll Liabilities:Payroll Tax - Office Staff""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""82"", ""value"": ""Profit Sharing Payable""}, {""value"": ""13708.33""}]}]}, ""type"": ""Section"", ""group"": ""OperatingAdjustments"", ""Header"": {""ColData"": [{""value"": ""Adjustments to reconcile Net Income to Net Cash provided by operations:""}, {""value"": """"}]}, ""Summary"": {""ColData"": [{""value"": ""Total Adjustments to reconcile Net Income to Net Cash provided by operations:""}, {""value"": ""-1809.47""}]}}]}, ""type"": ""Section"", ""group"": ""OperatingActivities"", ""Header"": {""ColData"": [{""value"": ""OPERATING ACTIVITIES""}, {""value"": """"}]}, ""Summary"": {""ColData"": [{""value"": ""Net cash provided by operating activities""}, {""value"": ""20370.56""}]}}, {""type"": ""Section"", ""group"": ""CashIncrease"", ""Summary"": {""ColData"": [{""value"": ""Net cash increase for period""}, {""value"": ""20370.56""}]}}, {""group"": ""BeginningCash"", ""ColData"": [{""value"": ""Cash at beginning of period""}, {""value"": ""284277.15""}]}, {""type"": ""Section"", ""group"": ""EndingCash"", ""Summary"": {""ColData"": [{""value"": ""Cash at end of period""}, {""value"": ""304647.71""}]}}]}, ""Header"": {""Time"": ""2025-09-04T22:30:18-07:00"", ""Option"": [{""Name"": ""NoReportData"", ""Value"": ""false""}], ""Currency"": ""USD"", ""EndPeriod"": ""2025-06-30"", ""ReportName"": ""CashFlow"", ""StartPeriod"": ""2025-06-01"", ""SummarizeColumnsBy"": ""Total""}, ""Columns"": {""Column"": [{""ColType"": ""Account"", ""ColTitle"": """"}, {""ColType"": ""Money"", ""ColTitle"": ""Total"", ""MetaData"": [{""Name"": ""ColKey"", ""Value"": ""total""}]}]}}",2025-09-05 11:00:17.769 +0530
11,CashFlow,,2025-07-01,2025-07-31,USD,2025-09-05 11:00:35.000 +0530,"{""Rows"": {""Row"": [{""Rows"": {""Row"": [{""type"": ""Data"", ""group"": ""NetIncome"", ""ColData"": [{""value"": ""Net Income""}, {""value"": ""30441.31""}]}, {""Rows"": {""Row"": [{""type"": ""Data"", ""ColData"": [{""id"": ""102"", ""value"": ""Accounts Receivable""}, {""value"": ""947.15""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""81"", ""value"": ""Accounts Payable""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Chase credit card #4012""}, {""value"": ""-5401.49""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""291"", ""value"": ""401K:401K - Dr. Canner""}, {""value"": ""576.92""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""356"", ""value"": ""401K:401K - Dr. Ghosh""}, {""value"": ""903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""295"", ""value"": ""401K:401K - Dr. Scherer""}, {""value"": ""903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""296"", ""value"": ""401K:401K - Dr. Shah""}, {""value"": ""903.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""401K:401K - Dr. Shaikh""}, {""value"": ""1236.84""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""289"", ""value"": ""401K:401K - Office Staff""}, {""value"": ""1351.98""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""98"", ""value"": ""Bank Loan""}, {""value"": ""-5385.05""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Direct Deposit Liabilities""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""**********"", ""value"": ""Doctor Admin Income Payable""}, {""value"": ""4425.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""336"", ""value"": ""Payroll Liabilities:BCBS Health""}, {""value"": ""104.80""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""357"", ""value"": ""Payroll Liabilities:Payroll Liabilities""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""353"", ""value"": ""Payroll Liabilities:Payroll Tax - Associate Dr. Shaikh""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""271"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Canner""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""360"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Ghosh""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""273"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Pierce""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""274"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Scherer""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""275"", ""value"": ""Payroll Liabilities:Payroll Tax - Dr. Shah""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""276"", ""value"": ""Payroll Liabilities:Payroll Tax - Office Staff""}, {""value"": ""0.00""}]}, {""type"": ""Data"", ""ColData"": [{""id"": ""82"", ""value"": ""Profit Sharing Payable""}, {""value"": ""13708.33""}]}]}, ""type"": ""Section"", ""group"": ""OperatingAdjustments"", ""Header"": {""ColData"": [{""value"": ""Adjustments to reconcile Net Income to Net Cash provided by operations:""}, {""value"": """"}]}, ""Summary"": {""ColData"": [{""value"": ""Total Adjustments to reconcile Net Income to Net Cash provided by operations:""}, {""value"": ""14276.00""}]}}]}, ""type"": ""Section"", ""group"": ""OperatingActivities"", ""Header"": {""ColData"": [{""value"": ""OPERATING ACTIVITIES""}, {""value"": """"}]}, ""Summary"": {""ColData"": [{""value"": ""Net cash provided by operating activities""}, {""value"": ""44717.31""}]}}, {""type"": ""Section"", ""group"": ""CashIncrease"", ""Summary"": {""ColData"": [{""value"": ""Net cash increase for period""}, {""value"": ""44717.31""}]}}, {""group"": ""BeginningCash"", ""ColData"": [{""value"": ""Cash at beginning of period""}, {""value"": ""304647.71""}]}, {""type"": ""Section"", ""group"": ""EndingCash"", ""Summary"": {""ColData"": [{""value"": ""Cash at end of period""}, {""value"": ""349365.02""}]}}]}, ""Header"": {""Time"": ""2025-09-04T22:30:35-07:00"", ""Option"": [{""Name"": ""NoReportData"", ""Value"": ""false""}], ""Currency"": ""USD"", ""EndPeriod"": ""2025-07-31"", ""ReportName"": ""CashFlow"", ""StartPeriod"": ""2025-07-01"", ""SummarizeColumnsBy"": ""Total""}, ""Columns"": {""Column"": [{""ColType"": ""Account"", ""ColTitle"": """"}, {""ColType"": ""Money"", ""ColTitle"": ""Total"", ""MetaData"": [{""Name"": ""ColKey"", ""Value"": ""total""}]}]}}",2025-09-05 11:00:34.062 +0530
