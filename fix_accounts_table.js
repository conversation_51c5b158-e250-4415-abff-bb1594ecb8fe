#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

async function fixAccountsTable() {
  await pg.connect();

  console.log('🔧 Fixing accounts table structure...');

  try {
    // First, let's check if there are any existing records
    const { rows: countRows } = await pg.query('SELECT COUNT(*) as count FROM accounts');
    console.log(`Current records in accounts table: ${countRows[0].count}`);
    
    // Drop the table and recreate it with proper structure
    console.log('Dropping and recreating accounts table with proper constraints...');
    
    await pg.query('DROP TABLE IF EXISTS accounts');
    
    await pg.query(`
      CREATE TABLE accounts (
        id text PRIMARY KEY,
        name text NOT NULL,
        fully_qualified_name text,
        account_type text,
        account_sub_type text,
        classification text,
        active boolean,
        currency text,
        raw jsonb,
        updated_at timestamptz NOT NULL DEFAULT now(),
        created_at timestamptz NOT NULL DEFAULT now()
      )
    `);
    
    console.log('✅ accounts table recreated with proper structure');
    
    // Verify the new structure
    const { rows: constraints } = await pg.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'accounts'
    `);
    
    console.log('\n🔒 New constraints:');
    constraints.forEach(row => {
      console.log(`  ${row.constraint_name}: ${row.constraint_type}`);
    });
    
  } catch (e) {
    console.error('❌ Error fixing table:', e.message);
  }

  await pg.end();
}

fixAccountsTable().catch(console.error);
