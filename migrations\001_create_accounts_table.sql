-- Migration: Create accounts table
-- Description: Stores QuickBooks account information
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS accounts (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    fully_qualified_name TEXT,
    account_type TEXT,
    account_sub_type TEXT,
    classification TEXT,
    active BOOLEAN,
    currency TEXT,
    raw JSONB,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_accounts_name ON accounts(name);
CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_accounts_sub_type ON accounts(account_sub_type);
CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(active);
CREATE INDEX IF NOT EXISTS idx_accounts_created_at ON accounts(created_at);

-- Add comments for documentation
COMMENT ON TABLE accounts IS 'Stores QuickBooks account information including chart of accounts data';
COMMENT ON COLUMN accounts.id IS 'QuickBooks account ID (primary key)';
COMMENT ON COLUMN accounts.name IS 'Account display name';
COMMENT ON COLUMN accounts.fully_qualified_name IS 'Full hierarchical account name';
COMMENT ON COLUMN accounts.account_type IS 'QuickBooks account type (Asset, Liability, Equity, Income, Expense)';
COMMENT ON COLUMN accounts.account_sub_type IS 'QuickBooks account sub-type';
COMMENT ON COLUMN accounts.classification IS 'Account classification';
COMMENT ON COLUMN accounts.active IS 'Whether the account is active in QuickBooks';
COMMENT ON COLUMN accounts.currency IS 'Account currency code';
COMMENT ON COLUMN accounts.raw IS 'Raw JSON data from QuickBooks API';
COMMENT ON COLUMN accounts.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN accounts.created_at IS 'Record creation timestamp';
