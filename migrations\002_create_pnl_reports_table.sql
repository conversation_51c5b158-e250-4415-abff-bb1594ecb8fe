-- Migration: Create pnl_reports table
-- Description: Stores P&L report metadata and raw data
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS pnl_reports (
    id BIGSERIAL PRIMARY KEY,
    report_name TEXT,
    report_basis TEXT,
    start_date DATE,
    end_date DATE,
    currency TEXT,
    generated_at TIMESTAMPTZ,
    raw JSONB,
    powerbi_kpi_json JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pnl_reports_start_date ON pnl_reports(start_date);
CREATE INDEX IF NOT EXISTS idx_pnl_reports_end_date ON pnl_reports(end_date);
CREATE INDEX IF NOT EXISTS idx_pnl_reports_date_range ON pnl_reports(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_pnl_reports_created_at ON pnl_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_pnl_reports_generated_at ON pnl_reports(generated_at);

-- Add comments for documentation
COMMENT ON TABLE pnl_reports IS 'Stores P&L report metadata and raw QuickBooks data';
COMMENT ON COLUMN pnl_reports.id IS 'Auto-generated primary key';
COMMENT ON COLUMN pnl_reports.report_name IS 'Name of the P&L report';
COMMENT ON COLUMN pnl_reports.report_basis IS 'Accounting basis (Cash, Accrual)';
COMMENT ON COLUMN pnl_reports.start_date IS 'Report period start date';
COMMENT ON COLUMN pnl_reports.end_date IS 'Report period end date';
COMMENT ON COLUMN pnl_reports.currency IS 'Report currency code';
COMMENT ON COLUMN pnl_reports.generated_at IS 'When the report was generated in QuickBooks';
COMMENT ON COLUMN pnl_reports.raw IS 'Raw JSON data from QuickBooks P&L API';
COMMENT ON COLUMN pnl_reports.powerbi_kpi_json IS 'Transformed KPI data for PowerBI integration';
COMMENT ON COLUMN pnl_reports.created_at IS 'Record creation timestamp';
