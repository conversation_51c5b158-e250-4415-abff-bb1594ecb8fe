-- Migration: Create pnl_lines table
-- Description: Stores detailed P&L line items
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS pnl_lines (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL REFERENCES pnl_reports(id) ON DELETE CASCADE,
    path TEXT,
    account_name TEXT,
    account_id TEXT,
    amount NUMERIC(18,2),
    category TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pnl_lines_report_id ON pnl_lines(report_id);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_account_name ON pnl_lines(account_name);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_account_id ON pnl_lines(account_id);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_category ON pnl_lines(category);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_amount ON pnl_lines(amount);
CREATE INDEX IF NOT EXISTS idx_pnl_lines_path ON pnl_lines(path);

-- Add foreign key constraint with proper naming
ALTER TABLE pnl_lines 
ADD CONSTRAINT fk_pnl_lines_report_id 
FOREIGN KEY (report_id) REFERENCES pnl_reports(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE pnl_lines IS 'Stores detailed P&L line items for each report';
COMMENT ON COLUMN pnl_lines.id IS 'Auto-generated primary key';
COMMENT ON COLUMN pnl_lines.report_id IS 'Reference to pnl_reports table';
COMMENT ON COLUMN pnl_lines.path IS 'Hierarchical path in P&L structure';
COMMENT ON COLUMN pnl_lines.account_name IS 'Display name of the account';
COMMENT ON COLUMN pnl_lines.account_id IS 'QuickBooks account ID for traceability';
COMMENT ON COLUMN pnl_lines.amount IS 'Line item amount (positive or negative)';
COMMENT ON COLUMN pnl_lines.category IS 'Categorization for grouping (Operating Expenses, Admin, etc.)';
