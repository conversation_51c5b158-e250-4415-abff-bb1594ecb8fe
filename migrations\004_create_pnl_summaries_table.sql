-- Migration: Create pnl_summaries table
-- Description: Stores P&L section totals and summaries
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS pnl_summaries (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL REFERENCES pnl_reports(id) ON DELETE CASCADE,
    "group" TEXT,
    label TEXT,
    path TEXT,
    amount NUMERIC(18,2)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pnl_summaries_report_id ON pnl_summaries(report_id);
CREATE INDEX IF NOT EXISTS idx_pnl_summaries_group ON pnl_summaries("group");
CREATE INDEX IF NOT EXISTS idx_pnl_summaries_label ON pnl_summaries(label);
CREATE INDEX IF NOT EXISTS idx_pnl_summaries_path ON pnl_summaries(path);
CREATE INDEX IF NOT EXISTS idx_pnl_summaries_amount ON pnl_summaries(amount);

-- Add foreign key constraint with proper naming
ALTER TABLE pnl_summaries 
ADD CONSTRAINT fk_pnl_summaries_report_id 
FOREIGN KEY (report_id) REFERENCES pnl_reports(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE pnl_summaries IS 'Stores P&L section totals and summary information';
COMMENT ON COLUMN pnl_summaries.id IS 'Auto-generated primary key';
COMMENT ON COLUMN pnl_summaries.report_id IS 'Reference to pnl_reports table';
COMMENT ON COLUMN pnl_summaries."group" IS 'P&L section group (Income, Expenses, etc.)';
COMMENT ON COLUMN pnl_summaries.label IS 'Summary line label';
COMMENT ON COLUMN pnl_summaries.path IS 'Hierarchical path in P&L structure';
COMMENT ON COLUMN pnl_summaries.amount IS 'Summary amount for the section';
