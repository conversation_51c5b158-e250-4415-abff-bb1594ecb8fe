-- Migration: Create pnl_ebitda table
-- Description: Stores EBITDA calculations for P&L reports
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS pnl_ebitda (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL UNIQUE REFERENCES pnl_reports(id) ON DELETE CASCADE,
    net_income NUMERIC(18,2) NOT NULL DEFAULT 0,
    interest_expense NUMERIC(18,2) NOT NULL DEFAULT 0,
    income_taxes NUMERIC(18,2) NOT NULL DEFAULT 0,
    depreciation NUMERIC(18,2) NOT NULL DEFAULT 0,
    amortization NUMERIC(18,2) NOT NULL DEFAULT 0,
    ebit NUMERIC(18,2) NOT NULL DEFAULT 0,
    ebitda NUMERIC(18,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pnl_ebitda_report_id ON pnl_ebitda(report_id);
CREATE INDEX IF NOT EXISTS idx_pnl_ebitda_ebitda ON pnl_ebitda(ebitda);
CREATE INDEX IF NOT EXISTS idx_pnl_ebitda_ebit ON pnl_ebitda(ebit);
CREATE INDEX IF NOT EXISTS idx_pnl_ebitda_net_income ON pnl_ebitda(net_income);
CREATE INDEX IF NOT EXISTS idx_pnl_ebitda_created_at ON pnl_ebitda(created_at);

-- Add foreign key constraint with proper naming
ALTER TABLE pnl_ebitda 
ADD CONSTRAINT fk_pnl_ebitda_report_id 
FOREIGN KEY (report_id) REFERENCES pnl_reports(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE pnl_ebitda IS 'Stores EBITDA calculations for P&L reports';
COMMENT ON COLUMN pnl_ebitda.id IS 'Auto-generated primary key';
COMMENT ON COLUMN pnl_ebitda.report_id IS 'Reference to pnl_reports table (unique)';
COMMENT ON COLUMN pnl_ebitda.net_income IS 'Net income from P&L';
COMMENT ON COLUMN pnl_ebitda.interest_expense IS 'Total interest expenses';
COMMENT ON COLUMN pnl_ebitda.income_taxes IS 'Total income tax expenses';
COMMENT ON COLUMN pnl_ebitda.depreciation IS 'Total depreciation expenses';
COMMENT ON COLUMN pnl_ebitda.amortization IS 'Total amortization expenses';
COMMENT ON COLUMN pnl_ebitda.ebit IS 'Earnings Before Interest and Taxes (Net Income + Interest + Taxes)';
COMMENT ON COLUMN pnl_ebitda.ebitda IS 'Earnings Before Interest, Taxes, Depreciation, and Amortization';
COMMENT ON COLUMN pnl_ebitda.created_at IS 'Record creation timestamp';
