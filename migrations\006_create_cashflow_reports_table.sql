-- Migration: Create cashflow_reports table
-- Description: Stores cashflow report metadata and raw data
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS cashflow_reports (
    id BIGSERIAL PRIMARY KEY,
    report_name TEXT,
    report_basis TEXT,
    start_date DATE,
    end_date DATE,
    currency TEXT,
    generated_at TIMESTAMPTZ,
    raw JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cashflow_reports_start_date ON cashflow_reports(start_date);
CREATE INDEX IF NOT EXISTS idx_cashflow_reports_end_date ON cashflow_reports(end_date);
CREATE INDEX IF NOT EXISTS idx_cashflow_reports_date_range ON cashflow_reports(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_cashflow_reports_created_at ON cashflow_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_cashflow_reports_generated_at ON cashflow_reports(generated_at);

-- Add comments for documentation
COMMENT ON TABLE cashflow_reports IS 'Stores cashflow report metadata and raw QuickBooks data';
COMMENT ON COLUMN cashflow_reports.id IS 'Auto-generated primary key';
COMMENT ON COLUMN cashflow_reports.report_name IS 'Name of the cashflow report';
COMMENT ON COLUMN cashflow_reports.report_basis IS 'Accounting basis (Cash, Accrual)';
COMMENT ON COLUMN cashflow_reports.start_date IS 'Report period start date';
COMMENT ON COLUMN cashflow_reports.end_date IS 'Report period end date';
COMMENT ON COLUMN cashflow_reports.currency IS 'Report currency code';
COMMENT ON COLUMN cashflow_reports.generated_at IS 'When the report was generated in QuickBooks';
COMMENT ON COLUMN cashflow_reports.raw IS 'Raw JSON data from QuickBooks Cashflow API';
COMMENT ON COLUMN cashflow_reports.created_at IS 'Record creation timestamp';
