-- Migration: Create cashflow_lines table
-- Description: Stores detailed cashflow line items
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS cashflow_lines (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL REFERENCES cashflow_reports(id) ON DELETE CASCADE,
    path TEXT,
    label TEXT,
    "group" TEXT,
    amount NUMERIC(18,2)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_report_id ON cashflow_lines(report_id);
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_group ON cashflow_lines("group");
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_label ON cashflow_lines(label);
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_path ON cashflow_lines(path);
CREATE INDEX IF NOT EXISTS idx_cashflow_lines_amount ON cashflow_lines(amount);

-- Add foreign key constraint with proper naming
ALTER TABLE cashflow_lines 
ADD CONSTRAINT fk_cashflow_lines_report_id 
FOREIGN KEY (report_id) REFERENCES cashflow_reports(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE cashflow_lines IS 'Stores detailed cashflow line items for each report';
COMMENT ON COLUMN cashflow_lines.id IS 'Auto-generated primary key';
COMMENT ON COLUMN cashflow_lines.report_id IS 'Reference to cashflow_reports table';
COMMENT ON COLUMN cashflow_lines.path IS 'Hierarchical path in cashflow structure';
COMMENT ON COLUMN cashflow_lines.label IS 'Line item label/description';
COMMENT ON COLUMN cashflow_lines."group" IS 'Cashflow category (Operating, Investing, Financing)';
COMMENT ON COLUMN cashflow_lines.amount IS 'Line item amount (positive or negative)';
