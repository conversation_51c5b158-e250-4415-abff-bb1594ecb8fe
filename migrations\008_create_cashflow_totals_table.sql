-- Migration: Create cashflow_totals table
-- Description: Stores cashflow summary totals by category
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS cashflow_totals (
    id BIGSERIAL PRIMARY KEY,
    report_id BIGINT NOT NULL UNIQUE REFERENCES cashflow_reports(id) ON DELETE CASCADE,
    operating NUMERIC(18,2) NOT NULL DEFAULT 0,
    investing NUMERIC(18,2) NOT NULL DEFAULT 0,
    financing NUMERIC(18,2) NOT NULL DEFAULT 0,
    net_cash_flow NUMERIC(18,2) NOT NULL DEFAULT 0,
    beginning_cash NUMERIC(18,2) NOT NULL DEFAULT 0,
    ending_cash NUMERIC(18,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cashflow_totals_report_id ON cashflow_totals(report_id);
CREATE INDEX IF NOT EXISTS idx_cashflow_totals_operating ON cashflow_totals(operating);
CREATE INDEX IF NOT EXISTS idx_cashflow_totals_net_cash_flow ON cashflow_totals(net_cash_flow);
CREATE INDEX IF NOT EXISTS idx_cashflow_totals_ending_cash ON cashflow_totals(ending_cash);
CREATE INDEX IF NOT EXISTS idx_cashflow_totals_created_at ON cashflow_totals(created_at);

-- Add foreign key constraint with proper naming
ALTER TABLE cashflow_totals 
ADD CONSTRAINT fk_cashflow_totals_report_id 
FOREIGN KEY (report_id) REFERENCES cashflow_reports(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE cashflow_totals IS 'Stores cashflow summary totals by category for each report';
COMMENT ON COLUMN cashflow_totals.id IS 'Auto-generated primary key';
COMMENT ON COLUMN cashflow_totals.report_id IS 'Reference to cashflow_reports table (unique)';
COMMENT ON COLUMN cashflow_totals.operating IS 'Total operating cash flow';
COMMENT ON COLUMN cashflow_totals.investing IS 'Total investing cash flow';
COMMENT ON COLUMN cashflow_totals.financing IS 'Total financing cash flow';
COMMENT ON COLUMN cashflow_totals.net_cash_flow IS 'Net cash flow (sum of all categories)';
COMMENT ON COLUMN cashflow_totals.beginning_cash IS 'Cash at beginning of period';
COMMENT ON COLUMN cashflow_totals.ending_cash IS 'Cash at end of period';
COMMENT ON COLUMN cashflow_totals.created_at IS 'Record creation timestamp';
