-- Migration: Create kpi_snapshots table
-- Description: Stores centralized KPI data in JSONB format
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS kpi_snapshots (
    id BIGSERIAL PRIMARY KEY,
    pnl_report_id BIGINT REFERENCES pnl_reports(id) ON DELETE SET NULL,
    cashflow_report_id BIGINT REFERENCES cashflow_reports(id) ON DELETE SET NULL,
    
    -- KPI payloads stored as JSONB for flexibility
    total_expense_kpi JSONB,
    total_revenue_kpi JSONB,
    ebitda_kpi JSONB,
    cashflow_kpi JSONB,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_pnl_report_id ON kpi_snapshots(pnl_report_id);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_cashflow_report_id ON kpi_snapshots(cashflow_report_id);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_created_at ON kpi_snapshots(created_at);

-- Create GIN indexes for JSONB columns for efficient querying
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_total_expense_kpi ON kpi_snapshots USING GIN (total_expense_kpi);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_total_revenue_kpi ON kpi_snapshots USING GIN (total_revenue_kpi);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_ebitda_kpi ON kpi_snapshots USING GIN (ebitda_kpi);
CREATE INDEX IF NOT EXISTS idx_kpi_snapshots_cashflow_kpi ON kpi_snapshots USING GIN (cashflow_kpi);

-- Add foreign key constraints with proper naming
ALTER TABLE kpi_snapshots 
ADD CONSTRAINT fk_kpi_snapshots_pnl_report_id 
FOREIGN KEY (pnl_report_id) REFERENCES pnl_reports(id) ON DELETE SET NULL;

ALTER TABLE kpi_snapshots 
ADD CONSTRAINT fk_kpi_snapshots_cashflow_report_id 
FOREIGN KEY (cashflow_report_id) REFERENCES cashflow_reports(id) ON DELETE SET NULL;

-- Add comments for documentation
COMMENT ON TABLE kpi_snapshots IS 'Centralized KPI storage with flexible JSONB format';
COMMENT ON COLUMN kpi_snapshots.id IS 'Auto-generated primary key';
COMMENT ON COLUMN kpi_snapshots.pnl_report_id IS 'Reference to pnl_reports table (nullable)';
COMMENT ON COLUMN kpi_snapshots.cashflow_report_id IS 'Reference to cashflow_reports table (nullable)';
COMMENT ON COLUMN kpi_snapshots.total_expense_kpi IS 'Total expense KPI data in JSON format';
COMMENT ON COLUMN kpi_snapshots.total_revenue_kpi IS 'Total revenue KPI data in JSON format';
COMMENT ON COLUMN kpi_snapshots.ebitda_kpi IS 'EBITDA KPI data in JSON format';
COMMENT ON COLUMN kpi_snapshots.cashflow_kpi IS 'Cashflow KPI data in JSON format';
COMMENT ON COLUMN kpi_snapshots.created_at IS 'KPI snapshot creation timestamp';
