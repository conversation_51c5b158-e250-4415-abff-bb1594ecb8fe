-- Migration: Create ebitda_account_map table
-- Description: Stores EBITDA account classification mappings
-- Created: 2025-09-11

CREATE TABLE IF NOT EXISTS ebitda_account_map (
    id BIGSERIAL PRIMARY KEY,
    account_id TEXT,
    account_name_pattern TEXT,
    account_sub_type TEXT,
    ebitda_category TEXT NOT NULL CHECK (ebitda_category IN ('Interest','Taxes','Depreciation','Amortization')),
    priority INT NOT NULL DEFAULT 100,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_account_id ON ebitda_account_map(account_id);
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_pattern ON ebitda_account_map(account_name_pattern);
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_category ON ebitda_account_map(ebitda_category);
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_priority ON ebitda_account_map(priority);
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_sub_type ON ebitda_account_map(account_sub_type);
CREATE INDEX IF NOT EXISTS idx_ebitda_account_map_created_at ON ebitda_account_map(created_at);

-- Create unique constraint to prevent duplicate patterns
CREATE UNIQUE INDEX IF NOT EXISTS idx_ebitda_account_map_unique_pattern 
ON ebitda_account_map(account_name_pattern, ebitda_category) 
WHERE account_name_pattern IS NOT NULL;

-- Add comments for documentation
COMMENT ON TABLE ebitda_account_map IS 'Stores account classification mappings for EBITDA calculations';
COMMENT ON COLUMN ebitda_account_map.id IS 'Auto-generated primary key';
COMMENT ON COLUMN ebitda_account_map.account_id IS 'Specific QuickBooks account ID (optional)';
COMMENT ON COLUMN ebitda_account_map.account_name_pattern IS 'Pattern to match account names (supports % wildcards)';
COMMENT ON COLUMN ebitda_account_map.account_sub_type IS 'QuickBooks account sub-type filter (optional)';
COMMENT ON COLUMN ebitda_account_map.ebitda_category IS 'EBITDA category: Interest, Taxes, Depreciation, or Amortization';
COMMENT ON COLUMN ebitda_account_map.priority IS 'Priority for pattern matching (lower number = higher priority)';
COMMENT ON COLUMN ebitda_account_map.created_at IS 'Record creation timestamp';
