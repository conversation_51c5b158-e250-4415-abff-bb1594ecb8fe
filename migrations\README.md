# PostgreSQL Database Migrations for QuickBooks Data System

This directory contains PostgreSQL migration files to create all the necessary tables for the QuickBooks data ingestion and KPI calculation system.

## Overview

The migration files create the following database schema:

### Core Data Tables
1. **`accounts`** - QuickBooks chart of accounts
2. **`pnl_reports`** - P&L report metadata and raw data
3. **`pnl_lines`** - Detailed P&L line items
4. **`pnl_summaries`** - P&L section totals
5. **`cashflow_reports`** - Cashflow report metadata and raw data
6. **`cashflow_lines`** - Detailed cashflow line items
7. **`cashflow_totals`** - Cashflow summary totals by category

### Analysis Tables
8. **`pnl_ebitda`** - EBITDA calculations for P&L reports
9. **`kpi_snapshots`** - Centralized KPI storage in JSONB format
10. **`ebitda_account_map`** - Account classification mappings for EBITDA

## Quick Start

### Option 1: Run All Migrations at Once
```bash
# Navigate to the migrations directory
cd migrations

# Run all migrations in the correct order
psql -d your_database_name -f run_all_migrations.sql
```

### Option 2: Run Individual Migrations
```bash
# Run migrations one by one in order
psql -d your_database_name -f 001_create_accounts_table.sql
psql -d your_database_name -f 002_create_pnl_reports_table.sql
psql -d your_database_name -f 003_create_pnl_lines_table.sql
# ... continue with remaining files
```

## Migration Files

| File | Description |
|------|-------------|
| `001_create_accounts_table.sql` | QuickBooks chart of accounts |
| `002_create_pnl_reports_table.sql` | P&L report metadata |
| `003_create_pnl_lines_table.sql` | P&L line items |
| `004_create_pnl_summaries_table.sql` | P&L section summaries |
| `005_create_pnl_ebitda_table.sql` | EBITDA calculations |
| `006_create_cashflow_reports_table.sql` | Cashflow report metadata |
| `007_create_cashflow_lines_table.sql` | Cashflow line items |
| `008_create_cashflow_totals_table.sql` | Cashflow totals |
| `009_create_kpi_snapshots_table.sql` | Centralized KPI storage |
| `010_create_ebitda_account_map_table.sql` | EBITDA account mappings |
| `run_all_migrations.sql` | Master script to run all migrations |

## Database Schema Relationships

```
accounts
    ↓ (referenced by account_id)
pnl_lines ← pnl_reports → pnl_summaries
    ↓                         ↓
pnl_ebitda              kpi_snapshots
                             ↑
cashflow_lines ← cashflow_reports → cashflow_totals
                             ↓
                        kpi_snapshots

ebitda_account_map (standalone mapping table)
```

## Key Features

### Indexes
- All tables include appropriate indexes for performance
- JSONB columns use GIN indexes for efficient querying
- Foreign key relationships are properly indexed

### Constraints
- Foreign key constraints with CASCADE/SET NULL as appropriate
- Check constraints for data validation (e.g., EBITDA categories)
- Unique constraints where needed

### Documentation
- Comprehensive table and column comments
- Clear naming conventions
- Migration tracking with `migration_history` table

## After Running Migrations

Once migrations are complete, you can:

1. **Ingest Data**:
   ```bash
   node ingest_accounts.js
   node ingest_pnl.js
   node ingest_cashflow.js
   ```

2. **Calculate KPIs**:
   ```bash
   node calculate_revenue.js
   node calculate_ebitda.js
   node calculate_cashflow.js
   ```

3. **Query Results**:
   ```bash
   node query_revenue.js
   node query_ebitda.js
   node query_cashflow.js
   ```

## Migration Tracking

The system includes a `migration_history` table that tracks:
- Which migrations have been executed
- When they were executed
- Success/failure status

You can check migration status with:
```sql
SELECT * FROM migration_history ORDER BY executed_at;
```

## Rollback

To rollback migrations, you can drop tables in reverse order:
```sql
DROP TABLE IF EXISTS ebitda_account_map CASCADE;
DROP TABLE IF EXISTS kpi_snapshots CASCADE;
DROP TABLE IF EXISTS cashflow_totals CASCADE;
DROP TABLE IF EXISTS cashflow_lines CASCADE;
DROP TABLE IF EXISTS cashflow_reports CASCADE;
DROP TABLE IF EXISTS pnl_ebitda CASCADE;
DROP TABLE IF EXISTS pnl_summaries CASCADE;
DROP TABLE IF EXISTS pnl_lines CASCADE;
DROP TABLE IF EXISTS pnl_reports CASCADE;
DROP TABLE IF EXISTS accounts CASCADE;
DROP TABLE IF EXISTS migration_history CASCADE;
```

## Environment Setup

Make sure your `.env` file contains the PostgreSQL connection details:
```
PGHOST=your_host
PGPORT=5432
PGDATABASE=your_database
PGUSER=your_username
PGPASSWORD=your_password
```
