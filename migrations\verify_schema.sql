-- Schema Verification Script
-- Description: Verifies that all tables and indexes were created correctly
-- Created: 2025-09-11
-- Usage: psql -d your_database -f verify_schema.sql

\echo '=== Database Schema Verification ==='
\echo ''

-- Check if all expected tables exist
\echo '1. Checking table existence...'
SELECT 
    CASE 
        WHEN COUNT(*) = 10 THEN '✅ All 10 tables created successfully'
        ELSE '❌ Missing tables: ' || (10 - COUNT(*))::text || ' tables missing'
    END as table_status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'accounts',
    'pnl_reports', 
    'pnl_lines',
    'pnl_summaries',
    'pnl_ebitda',
    'cashflow_reports',
    'cashflow_lines', 
    'cashflow_totals',
    'kpi_snapshots',
    'ebitda_account_map'
);

\echo ''
\echo '2. Table details:'
SELECT 
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
AND table_name IN (
    'accounts',
    'pnl_reports', 
    'pnl_lines',
    'pnl_summaries',
    'pnl_ebitda',
    'cashflow_reports',
    'cashflow_lines', 
    'cashflow_totals',
    'kpi_snapshots',
    'ebitda_account_map'
)
ORDER BY table_name;

\echo ''
\echo '3. Foreign key constraints:'
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

\echo ''
\echo '4. Index summary:'
SELECT 
    schemaname,
    tablename,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public'
AND tablename IN (
    'accounts',
    'pnl_reports', 
    'pnl_lines',
    'pnl_summaries',
    'pnl_ebitda',
    'cashflow_reports',
    'cashflow_lines', 
    'cashflow_totals',
    'kpi_snapshots',
    'ebitda_account_map'
)
GROUP BY schemaname, tablename
ORDER BY tablename;

\echo ''
\echo '5. Check constraints:'
SELECT 
    tc.table_name,
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_schema = 'public'
AND tc.constraint_type = 'CHECK'
ORDER BY tc.table_name;

\echo ''
\echo '6. Migration history:'
SELECT 
    COUNT(*) as total_migrations,
    COUNT(CASE WHEN success THEN 1 END) as successful_migrations,
    MAX(executed_at) as last_migration
FROM migration_history;

\echo ''
\echo '=== Verification Complete ==='
\echo 'If all checks pass, your database schema is ready for QuickBooks data ingestion!'
