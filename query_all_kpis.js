#!/usr/bin/env node
/**
 * Query all KPIs (Revenue, EBITDA, Cashflow) for comprehensive financial analysis.
 *
 * Usage:
 *   node query_all_kpis.js [pnl_report_id] [cashflow_report_id]
 *
 * If no report IDs provided, uses the latest reports.
 */

import "dotenv/config";
import { Client } from "pg";

const { PGHOST, PGPORT, PGDATABASE, PGUSER, PGPASSWORD } = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const PNL_REPORT_ID = process.argv[2];
const CASHFLOW_REPORT_ID = process.argv[3];

async function queryAllKPIs() {
  await pg.connect();

  let pnlReportId = PNL_REPORT_ID;
  let cashflowReportId = CASHFLOW_REPORT_ID;

  // Get latest P&L report if not specified
  if (!pnlReportId) {
    const { rows } = await pg.query(
      `SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1`
    );
    if (rows.length) pnlReportId = rows[0].id;
  }

  // Get latest cashflow report if not specified
  if (!cashflowReportId) {
    const { rows } = await pg.query(
      `SELECT id FROM cashflow_reports ORDER BY id DESC LIMIT 1`
    );
    if (rows.length) cashflowReportId = rows[0].id;
  }

  console.log("Comprehensive Financial KPI Dashboard");
  console.log("=====================================");
  console.log(`P&L Report ID: ${pnlReportId || "N/A"}`);
  console.log(`Cashflow Report ID: ${cashflowReportId || "N/A"}`);
  console.log("");

  // Get Revenue KPIs
  let revenueKpi = null;
  if (pnlReportId) {
    const { rows } = await pg.query(
      `
      SELECT pr.report_name, pr.start_date, pr.end_date, ks.total_revenue_kpi
      FROM pnl_reports pr
      LEFT JOIN kpi_snapshots ks ON pr.id = ks.pnl_report_id
      WHERE pr.id = $1
    `,
      [pnlReportId]
    );

    if (rows.length && rows[0].total_revenue_kpi) {
      revenueKpi = {
        report: rows[0],
        data: rows[0].total_revenue_kpi,
      };
    }
  }

  // Get EBITDA KPIs
  let ebitdaKpi = null;
  if (pnlReportId) {
    const { rows } = await pg.query(
      `
      SELECT ebitda_kpi
      FROM kpi_snapshots
      WHERE pnl_report_id = $1 AND ebitda_kpi IS NOT NULL
    `,
      [pnlReportId]
    );

    if (rows.length) {
      ebitdaKpi = rows[0].ebitda_kpi.summary; // Extract summary for compatibility
    }
  }

  // Get Cashflow KPIs
  let cashflowKpi = null;
  if (cashflowReportId) {
    const { rows } = await pg.query(
      `
      SELECT cr.report_name, cr.start_date, cr.end_date, ks.cashflow_kpi
      FROM cashflow_reports cr
      LEFT JOIN kpi_snapshots ks ON cr.id = ks.cashflow_report_id
      WHERE cr.id = $1
    `,
      [cashflowReportId]
    );

    if (rows.length && rows[0].cashflow_kpi) {
      cashflowKpi = {
        report: rows[0],
        data: rows[0].cashflow_kpi,
      };
    }
  }

  // Display Revenue Summary
  if (revenueKpi) {
    console.log("💰 REVENUE ANALYSIS");
    console.log("-------------------");
    console.log(
      `Period: ${revenueKpi.report.start_date} to ${revenueKpi.report.end_date}`
    );
    console.log(
      `Total Revenue: $${Number(
        revenueKpi.data.summary.total_revenue
      ).toLocaleString("en-US", { minimumFractionDigits: 2 })}`
    );
    console.log(
      `Practice Income: $${Number(
        revenueKpi.data.summary.practice_income
      ).toLocaleString("en-US", {
        minimumFractionDigits: 2,
      })} (${revenueKpi.data.metrics.practice_vs_other.practice_percentage.toFixed(
        1
      )}%)`
    );
    console.log(
      `Number of Doctors: ${revenueKpi.data.summary.number_of_doctors}`
    );
    console.log(
      `Top Doctor: ${
        revenueKpi.data.metrics.revenue_by_doctor[0]?.doctor || "N/A"
      } ($${Number(
        revenueKpi.data.metrics.revenue_by_doctor[0]?.amount || 0
      ).toLocaleString("en-US", { minimumFractionDigits: 2 })})`
    );
    console.log("");
  } else {
    console.log("💰 REVENUE ANALYSIS: No data available");
    console.log("   Run 'node calculate_revenue.js' to generate revenue KPIs");
    console.log("");
  }

  // Display EBITDA Summary
  if (ebitdaKpi) {
    console.log("📊 EBITDA ANALYSIS");
    console.log("------------------");
    console.log(
      `Net Income: $${Number(ebitdaKpi.net_income).toLocaleString("en-US", {
        minimumFractionDigits: 2,
      })}`
    );
    console.log(
      `EBIT: $${Number(ebitdaKpi.ebit).toLocaleString("en-US", {
        minimumFractionDigits: 2,
      })}`
    );
    console.log(
      `EBITDA: $${Number(ebitdaKpi.ebitda).toLocaleString("en-US", {
        minimumFractionDigits: 2,
      })}`
    );
    console.log(
      `Interest Expense: $${Number(ebitdaKpi.interest_expense).toLocaleString(
        "en-US",
        { minimumFractionDigits: 2 }
      )}`
    );

    if (revenueKpi) {
      const ebitdaMargin =
        (Number(ebitdaKpi.ebitda) /
          Number(revenueKpi.data.summary.total_revenue)) *
        100;
      console.log(`EBITDA Margin: ${ebitdaMargin.toFixed(1)}%`);
    }
    console.log("");
  } else {
    console.log("📊 EBITDA ANALYSIS: No data available");
    console.log("   Run 'node calculate_ebitda.js' to generate EBITDA metrics");
    console.log("");
  }

  // Display Cashflow Summary
  if (cashflowKpi) {
    console.log("💵 CASHFLOW ANALYSIS");
    console.log("--------------------");
    console.log(
      `Period: ${cashflowKpi.report.start_date} to ${cashflowKpi.report.end_date}`
    );
    console.log(
      `Operating Cash Flow: $${Number(
        cashflowKpi.data.summary.operating_cash_flow
      ).toLocaleString("en-US", { minimumFractionDigits: 2 })}`
    );
    console.log(
      `Net Cash Flow: $${Number(
        cashflowKpi.data.summary.net_cash_flow
      ).toLocaleString("en-US", { minimumFractionDigits: 2 })}`
    );
    console.log(
      `Ending Cash: $${Number(
        cashflowKpi.data.summary.ending_cash
      ).toLocaleString("en-US", { minimumFractionDigits: 2 })}`
    );
    console.log(
      `Cash Position: ${cashflowKpi.data.analysis.cash_position.toUpperCase()}`
    );
    console.log(
      `Cash Conversion Ratio: ${cashflowKpi.data.metrics.cash_conversion_ratio.toFixed(
        2
      )}`
    );
    console.log("");
  } else {
    console.log("💵 CASHFLOW ANALYSIS: No data available");
    console.log(
      "   Run 'node calculate_cashflow.js' to generate cashflow KPIs"
    );
    console.log("");
  }

  // Cross-KPI Analysis
  if (revenueKpi && ebitdaKpi && cashflowKpi) {
    console.log("🔍 CROSS-KPI ANALYSIS");
    console.log("---------------------");

    const revenue = Number(revenueKpi.data.summary.total_revenue);
    const netIncome = Number(ebitdaKpi.net_income);
    const ebitda = Number(ebitdaKpi.ebitda);
    const operatingCashFlow = Number(
      cashflowKpi.data.summary.operating_cash_flow
    );
    const endingCash = Number(cashflowKpi.data.summary.ending_cash);

    console.log(
      `Net Profit Margin: ${((netIncome / revenue) * 100).toFixed(1)}%`
    );
    console.log(`EBITDA Margin: ${((ebitda / revenue) * 100).toFixed(1)}%`);
    console.log(
      `Operating Cash Margin: ${((operatingCashFlow / revenue) * 100).toFixed(
        1
      )}%`
    );
    console.log(`Cash-to-Revenue Ratio: ${(endingCash / revenue).toFixed(2)}`);
    console.log(
      `Revenue Quality Score: ${
        operatingCashFlow > netIncome ? "HIGH" : "MODERATE"
      } (Cash Flow vs Net Income)`
    );

    // Financial Health Score
    let healthScore = 0;
    if (netIncome > 0) healthScore += 25;
    if (ebitda > netIncome) healthScore += 25;
    if (operatingCashFlow > 0) healthScore += 25;
    if (endingCash > revenue * 0.1) healthScore += 25;

    console.log(`Financial Health Score: ${healthScore}/100`);

    const healthStatus =
      healthScore >= 75
        ? "EXCELLENT"
        : healthScore >= 50
        ? "GOOD"
        : healthScore >= 25
        ? "FAIR"
        : "POOR";
    console.log(`Overall Financial Health: ${healthStatus}`);
    console.log("");
  }

  // Key Recommendations
  console.log("💡 KEY INSIGHTS & RECOMMENDATIONS");
  console.log("---------------------------------");

  if (revenueKpi) {
    const topDoctor = revenueKpi.data.metrics.revenue_by_doctor[0];
    const bottomDoctor =
      revenueKpi.data.metrics.revenue_by_doctor[
        revenueKpi.data.metrics.revenue_by_doctor.length - 1
      ];

    if (
      topDoctor &&
      bottomDoctor &&
      topDoctor.amount > bottomDoctor.amount * 2
    ) {
      console.log(
        `• Revenue Concentration: Dr. ${
          topDoctor.doctor
        } generates ${topDoctor.percentage.toFixed(
          1
        )}% of revenue - consider diversification strategies`
      );
    }

    if (revenueKpi.data.metrics.practice_vs_other.other_percentage < 0) {
      console.log(
        `• Other Income: Negative other income (${revenueKpi.data.metrics.practice_vs_other.other_percentage.toFixed(
          1
        )}%) - review non-practice expenses`
      );
    }
  }

  if (ebitdaKpi && Number(ebitdaKpi.interest_expense) > 0) {
    console.log(
      `• Interest Expense: $${Number(
        ebitdaKpi.interest_expense
      ).toLocaleString()} in interest costs - consider debt optimization`
    );
  }

  if (cashflowKpi) {
    const cashConversion = cashflowKpi.data.metrics.cash_conversion_ratio;
    if (cashConversion > 2) {
      console.log(
        `• Cash Conversion: Excellent cash conversion ratio (${cashConversion.toFixed(
          2
        )}) - strong collections process`
      );
    } else if (cashConversion < 1) {
      console.log(
        `• Cash Conversion: Low cash conversion ratio (${cashConversion.toFixed(
          2
        )}) - review accounts receivable management`
      );
    }

    if (cashflowKpi.data.analysis.cash_position === "strong") {
      console.log(
        `• Cash Position: Strong cash position - consider investment opportunities or debt reduction`
      );
    }
  }

  console.log("");
  console.log("📈 Next Steps:");
  console.log(
    "   • Review detailed reports: node query_revenue.js, node query_ebitda.js, node query_cashflow.js"
  );
  console.log(
    "   • Update KPIs: node calculate_revenue.js all, node calculate_ebitda.js all, node calculate_cashflow.js all"
  );
  console.log("   • Monitor trends by running this analysis monthly");

  await pg.end();
}

queryAllKPIs().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
