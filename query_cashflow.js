#!/usr/bin/env node
/**
 * Query cashflow KPI data from the database for analysis and reporting.
 * 
 * Usage:
 *   node query_cashflow.js [report_id]
 *   
 * If no report_id is provided, shows the latest cashflow report.
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter

async function queryCashflow() {
  await pg.connect();
  
  let reportId = REPORT_ID;
  
  // If no report_id provided, get the latest one
  if (!reportId) {
    const { rows } = await pg.query(`
      SELECT id FROM cashflow_reports ORDER BY id DESC LIMIT 1
    `);
    if (!rows.length) {
      console.log("No cashflow reports found in database.");
      await pg.end();
      return;
    }
    reportId = rows[0].id;
  }
  
  // Get report header info
  const { rows: reportRows } = await pg.query(`
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM cashflow_reports 
    WHERE id = $1
  `, [reportId]);
  
  if (!reportRows.length) {
    console.log(`Cashflow report with ID ${reportId} not found.`);
    await pg.end();
    return;
  }
  
  const report = reportRows[0];
  
  // Get cashflow KPI data
  const { rows: kpiRows } = await pg.query(`
    SELECT cashflow_kpi, created_at
    FROM kpi_snapshots 
    WHERE cashflow_report_id = $1 AND cashflow_kpi IS NOT NULL
  `, [reportId]);
  
  if (!kpiRows.length) {
    console.log(`No cashflow KPI data found for report ID ${reportId}.`);
    console.log("Run 'node calculate_cashflow.js' to calculate cashflow KPI for this report.");
    await pg.end();
    return;
  }
  
  const cashflowKpi = kpiRows[0].cashflow_kpi;
  
  // Display results
  console.log("Cashflow Analysis Report");
  console.log("========================");
  console.log(`Report ID: ${reportId}`);
  console.log(`Report Name: ${report.report_name}`);
  console.log(`Period: ${report.start_date} to ${report.end_date}`);
  console.log(`Currency: ${report.currency}`);
  console.log(`Generated: ${new Date(report.generated_at).toLocaleString()}`);
  console.log(`Cashflow KPI Calculated: ${new Date(kpiRows[0].created_at).toLocaleString()}`);
  console.log("");
  
  console.log("Cash Flow Summary:");
  console.log("------------------");
  console.log(`Operating Cash Flow:  $${Number(cashflowKpi.summary.operating_cash_flow).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Investing Cash Flow:  $${Number(cashflowKpi.summary.investing_cash_flow).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Financing Cash Flow:  $${Number(cashflowKpi.summary.financing_cash_flow).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Net Cash Flow:        $${Number(cashflowKpi.summary.net_cash_flow).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Beginning Cash:       $${Number(cashflowKpi.summary.beginning_cash).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Ending Cash:          $${Number(cashflowKpi.summary.ending_cash).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Net Income:           $${Number(cashflowKpi.summary.net_income).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log("");
  
  console.log("Key Metrics:");
  console.log("------------");
  console.log(`Cash Conversion Ratio: ${cashflowKpi.metrics.cash_conversion_ratio.toFixed(2)}`);
  console.log(`Operating Margin:      ${cashflowKpi.metrics.operating_margin.toFixed(1)}%`);
  console.log(`Cash Efficiency:       ${cashflowKpi.metrics.cash_efficiency.toFixed(1)}%`);
  
  if (cashflowKpi.metrics.cash_burn_rate > 0) {
    console.log(`Cash Burn Rate:        $${Number(cashflowKpi.metrics.cash_burn_rate).toLocaleString('en-US', {minimumFractionDigits: 2})} per period`);
    if (cashflowKpi.metrics.cash_runway_months) {
      console.log(`Cash Runway:           ${cashflowKpi.metrics.cash_runway_months.toFixed(1)} months`);
    }
  }
  console.log("");
  
  console.log("Analysis:");
  console.log("---------");
  console.log(`Cash Position:         ${cashflowKpi.analysis.cash_position.toUpperCase()}`);
  console.log(`Operating Trend:       ${cashflowKpi.analysis.operating_trend.toUpperCase()}`);
  console.log(`Liquidity Status:      ${cashflowKpi.analysis.liquidity_status.toUpperCase()}`);
  console.log("");
  
  // Show major operating items
  if (cashflowKpi.analysis.major_operating_items && cashflowKpi.analysis.major_operating_items.length > 0) {
    console.log("Major Operating Cash Flow Items:");
    console.log("--------------------------------");
    cashflowKpi.analysis.major_operating_items.forEach((item, index) => {
      const impact = item.impact === 'positive' ? '↑' : '↓';
      console.log(`${index + 1}. ${item.description}: $${Number(item.amount).toLocaleString('en-US', {minimumFractionDigits: 2})} ${impact}`);
    });
    console.log("");
  }
  
  // Show period comparison if available
  if (cashflowKpi.period_comparison) {
    console.log("Period-over-Period Comparison:");
    console.log("------------------------------");
    const comp = cashflowKpi.period_comparison;
    const operatingChange = comp.operating_change >= 0 ? '+' : '';
    const netCashChange = comp.net_cash_change >= 0 ? '+' : '';
    const endingCashChange = comp.ending_cash_change >= 0 ? '+' : '';
    
    console.log(`Operating Cash Flow Change: ${operatingChange}$${Number(comp.operating_change).toLocaleString('en-US', {minimumFractionDigits: 2})} (${comp.operating_change_percent.toFixed(1)}%)`);
    console.log(`Net Cash Flow Change:       ${netCashChange}$${Number(comp.net_cash_change).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log(`Ending Cash Change:         ${endingCashChange}$${Number(comp.ending_cash_change).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  // Show detailed breakdown
  console.log("Detailed Breakdown:");
  console.log("-------------------");
  
  if (cashflowKpi.breakdown.operating_activities.major_items.length > 0) {
    console.log("Operating Activities (Major Items):");
    cashflowKpi.breakdown.operating_activities.major_items.slice(0, 10).forEach(item => {
      console.log(`  ${item.description}: $${Number(item.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    });
    console.log(`  Total Operating: $${Number(cashflowKpi.breakdown.operating_activities.total).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  if (cashflowKpi.breakdown.investing_activities.items.length > 0) {
    console.log("Investing Activities:");
    cashflowKpi.breakdown.investing_activities.items.forEach(item => {
      console.log(`  ${item.description}: $${Number(item.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    });
    console.log(`  Total Investing: $${Number(cashflowKpi.breakdown.investing_activities.total).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  if (cashflowKpi.breakdown.financing_activities.items.length > 0) {
    console.log("Financing Activities:");
    cashflowKpi.breakdown.financing_activities.items.forEach(item => {
      console.log(`  ${item.description}: $${Number(item.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    });
    console.log(`  Total Financing: $${Number(cashflowKpi.breakdown.financing_activities.total).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  // Cash flow formula verification
  console.log("Formula Verification:");
  console.log("---------------------");
  console.log(`Net Cash Flow = Operating + Investing + Financing`);
  console.log(`              = $${Number(cashflowKpi.summary.operating_cash_flow).toFixed(2)} + $${Number(cashflowKpi.summary.investing_cash_flow).toFixed(2)} + $${Number(cashflowKpi.summary.financing_cash_flow).toFixed(2)}`);
  console.log(`              = $${Number(cashflowKpi.summary.net_cash_flow).toFixed(2)}`);
  console.log("");
  console.log(`Ending Cash = Beginning Cash + Net Cash Flow`);
  console.log(`            = $${Number(cashflowKpi.summary.beginning_cash).toFixed(2)} + $${Number(cashflowKpi.summary.net_cash_flow).toFixed(2)}`);
  console.log(`            = $${Number(cashflowKpi.summary.ending_cash).toFixed(2)}`);
  
  await pg.end();
}

queryCashflow().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
