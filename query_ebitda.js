#!/usr/bin/env node
/**
 * Query EBITDA data from the database for analysis and reporting.
 *
 * Usage:
 *   node query_ebitda.js [report_id]
 *
 * If no report_id is provided, shows the latest report.
 */

import "dotenv/config";
import { Client } from "pg";

const { PGHOST, PGPORT, PGDATABASE, PGUSER, PGPASSWORD } = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter

async function queryEBITDA() {
  await pg.connect();

  let reportId = REPORT_ID;

  // If no report_id provided, get the latest one
  if (!reportId) {
    const { rows } = await pg.query(`
      SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1
    `);
    if (!rows.length) {
      console.log("No P&L reports found in database.");
      await pg.end();
      return;
    }
    reportId = rows[0].id;
  }

  // Get report header info
  const { rows: reportRows } = await pg.query(
    `
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM pnl_reports 
    WHERE id = $1
  `,
    [reportId]
  );

  if (!reportRows.length) {
    console.log(`Report with ID ${reportId} not found.`);
    await pg.end();
    return;
  }

  const report = reportRows[0];

  // Get EBITDA data from KPI snapshots
  const { rows: ebitdaRows } = await pg.query(
    `
    SELECT ebitda_kpi, created_at
    FROM kpi_snapshots
    WHERE pnl_report_id = $1 AND ebitda_kpi IS NOT NULL
  `,
    [reportId]
  );

  if (!ebitdaRows.length) {
    console.log(`No EBITDA data found for report ID ${reportId}.`);
    console.log(
      "Run 'node calculate_ebitda.js' to calculate EBITDA for this report."
    );
    await pg.end();
    return;
  }

  const ebitdaKpi = ebitdaRows[0].ebitda_kpi;
  const ebitda = ebitdaKpi.summary; // Extract summary for backward compatibility

  // Display results
  console.log("EBITDA Analysis Report");
  console.log("======================");
  console.log(`Report ID: ${reportId}`);
  console.log(`Report Name: ${report.report_name}`);
  console.log(`Period: ${report.start_date} to ${report.end_date}`);
  console.log(`Currency: ${report.currency}`);
  console.log(`Generated: ${new Date(report.generated_at).toLocaleString()}`);
  console.log(
    `EBITDA Calculated: ${new Date(ebitdaRows[0].created_at).toLocaleString()}`
  );
  console.log("");

  console.log("Financial Metrics:");
  console.log("------------------");
  console.log(
    `Net Income:        $${Number(ebitda.net_income).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log(
    `Interest Expense:  $${Number(ebitda.interest_expense).toLocaleString(
      "en-US",
      { minimumFractionDigits: 2 }
    )}`
  );
  console.log(
    `Income Taxes:      $${Number(ebitda.income_taxes).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log(
    `Depreciation:      $${Number(ebitda.depreciation).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log(
    `Amortization:      $${Number(ebitda.amortization).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log("");
  console.log(
    `EBIT:              $${Number(ebitda.ebit).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log(
    `EBITDA:            $${Number(ebitda.ebitda).toLocaleString("en-US", {
      minimumFractionDigits: 2,
    })}`
  );
  console.log("");

  // Show enhanced metrics if available
  if (ebitdaKpi.metrics) {
    console.log("Advanced Metrics:");
    console.log("-----------------");
    if (ebitdaKpi.metrics.interest_coverage_ratio !== null) {
      console.log(
        `Interest Coverage Ratio: ${ebitdaKpi.metrics.interest_coverage_ratio.toFixed(
          2
        )}x`
      );
    }
    console.log(
      `Tax Efficiency: ${ebitdaKpi.metrics.tax_efficiency.toFixed(1)}%`
    );
    console.log("");
  }

  // Show analysis if available
  if (ebitdaKpi.analysis) {
    console.log("Analysis:");
    console.log("---------");
    console.log(
      `Profitability Trend: ${ebitdaKpi.analysis.profitability_trend.toUpperCase()}`
    );
    console.log(
      `Interest Burden: ${ebitdaKpi.analysis.interest_burden.toUpperCase()}`
    );
    console.log(
      `Operational Efficiency: ${ebitdaKpi.analysis.operational_efficiency.toUpperCase()}`
    );
    console.log("");
  }

  // Calculate some ratios if we have revenue data
  const { rows: incomeRows } = await pg.query(
    `
    SELECT SUM(amount) as total_income
    FROM pnl_lines 
    WHERE report_id = $1 AND path LIKE 'Income%'
  `,
    [reportId]
  );

  if (incomeRows.length && incomeRows[0].total_income) {
    const totalIncome = Number(incomeRows[0].total_income);
    const ebitdaMargin = (Number(ebitda.ebitda) / totalIncome) * 100;
    const ebitMargin = (Number(ebitda.ebit) / totalIncome) * 100;
    const netMargin = (Number(ebitda.net_income) / totalIncome) * 100;

    console.log("Key Ratios:");
    console.log("-----------");
    console.log(
      `Total Revenue:     $${totalIncome.toLocaleString("en-US", {
        minimumFractionDigits: 2,
      })}`
    );
    console.log(`Net Margin:        ${netMargin.toFixed(2)}%`);
    console.log(`EBIT Margin:       ${ebitMargin.toFixed(2)}%`);
    console.log(`EBITDA Margin:     ${ebitdaMargin.toFixed(2)}%`);
    console.log("");
  }

  console.log("Formula Verification:");
  console.log("---------------------");
  console.log(`EBIT = Net Income + Interest + Taxes`);
  console.log(
    `     = ${Number(ebitda.net_income).toFixed(2)} + ${Number(
      ebitda.interest_expense
    ).toFixed(2)} + ${Number(ebitda.income_taxes).toFixed(2)}`
  );
  console.log(`     = ${Number(ebitda.ebit).toFixed(2)}`);
  console.log("");
  console.log(`EBITDA = EBIT + Depreciation + Amortization`);
  console.log(
    `       = ${Number(ebitda.ebit).toFixed(2)} + ${Number(
      ebitda.depreciation
    ).toFixed(2)} + ${Number(ebitda.amortization).toFixed(2)}`
  );
  console.log(`       = ${Number(ebitda.ebitda).toFixed(2)}`);

  await pg.end();
}

queryEBITDA().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
