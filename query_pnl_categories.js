#!/usr/bin/env node
/**
 * Query P&L data by categories
 * Shows detailed breakdown of expenses by category
 * 
 * Usage:
 *   node query_pnl_categories.js [report_id]
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2];

async function queryPnLCategories() {
  await pg.connect();
  
  let reportId = REPORT_ID;
  
  // Get latest report if not specified
  if (!reportId) {
    const { rows } = await pg.query(`SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1`);
    if (rows.length) {
      reportId = rows[0].id;
      console.log(`No report ID specified, using latest report: ${reportId}`);
    } else {
      console.log("No P&L reports found in database.");
      await pg.end();
      return;
    }
  }
  
  // Get report info
  const { rows: reportRows } = await pg.query(`
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM pnl_reports 
    WHERE id = $1
  `, [reportId]);
  
  if (!reportRows.length) {
    console.log(`Report ID ${reportId} not found.`);
    await pg.end();
    return;
  }
  
  const report = reportRows[0];
  
  console.log("");
  console.log("P&L Category Analysis Report");
  console.log("============================");
  console.log(`Report ID: ${reportId}`);
  console.log(`Report Name: ${report.report_name}`);
  console.log(`Period: ${report.start_date} to ${report.end_date}`);
  console.log(`Currency: ${report.currency}`);
  console.log(`Generated: ${new Date(report.generated_at).toLocaleString()}`);
  console.log("");
  
  // Get category summary for this report
  const { rows: categoryRows } = await pg.query(`
    SELECT 
      category,
      COUNT(*) as line_count,
      COUNT(DISTINCT account_name) as unique_accounts,
      SUM(amount) as total_amount,
      AVG(amount) as avg_amount,
      MIN(amount) as min_amount,
      MAX(amount) as max_amount
    FROM pnl_lines 
    WHERE report_id = $1 AND category IS NOT NULL
    GROUP BY category
    ORDER BY ABS(SUM(amount)) DESC
  `, [reportId]);
  
  if (!categoryRows.length) {
    console.log("No categorized P&L data found for this report.");
    console.log("Run 'node update_pnl_categories.js' to add categories to existing data.");
    await pg.end();
    return;
  }
  
  console.log("Category Summary:");
  console.log("-----------------");
  let totalCategorized = 0;
  categoryRows.forEach(row => {
    totalCategorized += Math.abs(Number(row.total_amount));
    console.log(`${row.category}:`);
    console.log(`  Total Amount: $${Number(row.total_amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log(`  Line Items: ${row.line_count}`);
    console.log(`  Unique Accounts: ${row.unique_accounts}`);
    console.log(`  Average: $${Number(row.avg_amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  });
  
  // Show detailed breakdown for each category
  console.log("Detailed Category Breakdown:");
  console.log("----------------------------");
  
  for (const category of categoryRows) {
    console.log(`\n📊 ${category.category.toUpperCase()}`);
    console.log("=" + "=".repeat(category.category.length + 3));
    
    const { rows: accountRows } = await pg.query(`
      SELECT 
        account_name,
        SUM(amount) as total_amount,
        COUNT(*) as line_count,
        AVG(amount) as avg_amount
      FROM pnl_lines 
      WHERE report_id = $1 AND category = $2
      GROUP BY account_name
      ORDER BY ABS(SUM(amount)) DESC
    `, [reportId, category.category]);
    
    accountRows.forEach(row => {
      console.log(`  ${row.account_name}:`);
      console.log(`    Amount: $${Number(row.total_amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
      if (row.line_count > 1) {
        console.log(`    Entries: ${row.line_count} (avg: $${Number(row.avg_amount).toLocaleString('en-US', {minimumFractionDigits: 2})})`);
      }
    });
    
    console.log(`\n  Category Total: $${Number(category.total_amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  }
  
  // Show uncategorized items
  const { rows: uncategorizedRows } = await pg.query(`
    SELECT 
      account_name,
      SUM(amount) as total_amount,
      COUNT(*) as line_count
    FROM pnl_lines 
    WHERE report_id = $1 AND (category IS NULL OR category = '')
    GROUP BY account_name
    ORDER BY ABS(SUM(amount)) DESC
  `, [reportId]);
  
  if (uncategorizedRows.length > 0) {
    console.log("\n⚠️  UNCATEGORIZED ITEMS");
    console.log("======================");
    let uncategorizedTotal = 0;
    uncategorizedRows.forEach(row => {
      uncategorizedTotal += Math.abs(Number(row.total_amount));
      console.log(`  ${row.account_name}: $${Number(row.total_amount).toLocaleString('en-US', {minimumFractionDigits: 2})} (${row.line_count} entries)`);
    });
    console.log(`\n  Uncategorized Total: $${uncategorizedTotal.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  }
  
  // Summary statistics
  const { rows: totalRows } = await pg.query(`
    SELECT 
      COUNT(*) as total_lines,
      COUNT(DISTINCT account_name) as total_accounts,
      SUM(CASE WHEN category IS NOT NULL THEN 1 ELSE 0 END) as categorized_lines,
      SUM(CASE WHEN category IS NULL THEN 1 ELSE 0 END) as uncategorized_lines
    FROM pnl_lines 
    WHERE report_id = $1
  `, [reportId]);
  
  const stats = totalRows[0];
  const categorizationRate = (Number(stats.categorized_lines) / Number(stats.total_lines) * 100).toFixed(1);
  
  console.log("\n📈 CATEGORIZATION STATISTICS");
  console.log("============================");
  console.log(`Total P&L Lines: ${stats.total_lines}`);
  console.log(`Total Unique Accounts: ${stats.total_accounts}`);
  console.log(`Categorized Lines: ${stats.categorized_lines} (${categorizationRate}%)`);
  console.log(`Uncategorized Lines: ${stats.uncategorized_lines}`);
  console.log(`Categories Found: ${categoryRows.length}`);
  console.log(`Total Categorized Amount: $${totalCategorized.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  
  console.log("\n💡 INSIGHTS");
  console.log("===========");
  
  // Top expense category
  const topCategory = categoryRows[0];
  console.log(`• Largest expense category: ${topCategory.category} ($${Math.abs(Number(topCategory.total_amount)).toLocaleString('en-US', {minimumFractionDigits: 2})})`);
  
  // Category diversity
  if (categoryRows.length >= 3) {
    console.log(`• Expense diversity: ${categoryRows.length} different expense categories`);
  }
  
  // Categorization completeness
  if (categorizationRate >= 90) {
    console.log(`• Excellent categorization: ${categorizationRate}% of lines are categorized`);
  } else if (categorizationRate >= 70) {
    console.log(`• Good categorization: ${categorizationRate}% of lines are categorized`);
  } else {
    console.log(`• Needs improvement: Only ${categorizationRate}% of lines are categorized`);
  }
  
  console.log("\n📋 NEXT STEPS");
  console.log("=============");
  console.log("• Review uncategorized items and add them to the category mapping");
  console.log("• Use categories for expense budgeting and variance analysis");
  console.log("• Compare category totals across different reporting periods");
  console.log("• Set up category-based alerts for unusual spending patterns");
  
  await pg.end();
}

queryPnLCategories().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
