#!/usr/bin/env node
/**
 * Query revenue KPI data from the database for analysis and reporting.
 * 
 * Usage:
 *   node query_revenue.js [report_id]
 *   
 * If no report_id is provided, shows the latest report.
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // Optional report_id parameter

async function queryRevenue() {
  await pg.connect();
  
  let reportId = REPORT_ID;
  
  // If no report_id provided, get the latest one
  if (!reportId) {
    const { rows } = await pg.query(`
      SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1
    `);
    if (!rows.length) {
      console.log("No P&L reports found in database.");
      await pg.end();
      return;
    }
    reportId = rows[0].id;
  }
  
  // Get report header info
  const { rows: reportRows } = await pg.query(`
    SELECT report_name, start_date, end_date, currency, generated_at
    FROM pnl_reports 
    WHERE id = $1
  `, [reportId]);
  
  if (!reportRows.length) {
    console.log(`Report with ID ${reportId} not found.`);
    await pg.end();
    return;
  }
  
  const report = reportRows[0];
  
  // Get revenue KPI data
  const { rows: kpiRows } = await pg.query(`
    SELECT total_revenue_kpi, created_at
    FROM kpi_snapshots 
    WHERE pnl_report_id = $1 AND total_revenue_kpi IS NOT NULL
  `, [reportId]);
  
  if (!kpiRows.length) {
    console.log(`No revenue KPI data found for report ID ${reportId}.`);
    console.log("Run 'node calculate_revenue.js' to calculate revenue KPI for this report.");
    await pg.end();
    return;
  }
  
  const revenueKpi = kpiRows[0].total_revenue_kpi;
  
  // Display results
  console.log("Revenue Analysis Report");
  console.log("=======================");
  console.log(`Report ID: ${reportId}`);
  console.log(`Report Name: ${report.report_name}`);
  console.log(`Period: ${report.start_date} to ${report.end_date}`);
  console.log(`Currency: ${report.currency}`);
  console.log(`Generated: ${new Date(report.generated_at).toLocaleString()}`);
  console.log(`Revenue KPI Calculated: ${new Date(kpiRows[0].created_at).toLocaleString()}`);
  console.log("");
  
  console.log("Revenue Summary:");
  console.log("----------------");
  console.log(`Total Revenue:     $${Number(revenueKpi.summary.total_revenue).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
  console.log(`Practice Income:   $${Number(revenueKpi.summary.practice_income).toLocaleString('en-US', {minimumFractionDigits: 2})} (${revenueKpi.metrics.practice_vs_other.practice_percentage.toFixed(1)}%)`);
  console.log(`Other Income:      $${Number(revenueKpi.summary.other_income).toLocaleString('en-US', {minimumFractionDigits: 2})} (${revenueKpi.metrics.practice_vs_other.other_percentage.toFixed(1)}%)`);
  console.log(`Number of Doctors: ${revenueKpi.summary.number_of_doctors}`);
  console.log("");
  
  // Show revenue by doctor
  if (revenueKpi.metrics.revenue_by_doctor && revenueKpi.metrics.revenue_by_doctor.length > 0) {
    console.log("Revenue by Doctor:");
    console.log("------------------");
    revenueKpi.metrics.revenue_by_doctor.forEach((doctor, index) => {
      console.log(`${index + 1}. Dr. ${doctor.doctor}: $${Number(doctor.amount).toLocaleString('en-US', {minimumFractionDigits: 2})} (${doctor.percentage.toFixed(1)}%)`);
    });
    console.log("");
  }
  
  // Show top revenue sources
  if (revenueKpi.metrics.top_revenue_sources && revenueKpi.metrics.top_revenue_sources.length > 0) {
    console.log("Top Revenue Sources:");
    console.log("--------------------");
    revenueKpi.metrics.top_revenue_sources.slice(0, 10).forEach((source, index) => {
      const percentage = (source.amount / revenueKpi.summary.total_revenue * 100).toFixed(1);
      console.log(`${index + 1}. ${source.source}: $${Number(source.amount).toLocaleString('en-US', {minimumFractionDigits: 2})} (${percentage}%) - ${source.category}`);
    });
    console.log("");
  }
  
  // Show detailed breakdown
  console.log("Detailed Breakdown:");
  console.log("-------------------");
  
  if (revenueKpi.breakdown.practice_income.doctors) {
    console.log("Practice Income by Doctor:");
    Object.entries(revenueKpi.breakdown.practice_income.doctors).forEach(([doctor, amount]) => {
      console.log(`  Dr. ${doctor}: $${Number(amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    });
    
    if (revenueKpi.breakdown.practice_income.office_staff > 0) {
      console.log(`  Office Staff: $${Number(revenueKpi.breakdown.practice_income.office_staff).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    }
    console.log(`  Total Practice Income: $${Number(revenueKpi.breakdown.practice_income.total).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  if (revenueKpi.breakdown.other_income.items && Object.keys(revenueKpi.breakdown.other_income.items).length > 0) {
    console.log("Other Income Sources:");
    Object.entries(revenueKpi.breakdown.other_income.items).forEach(([source, amount]) => {
      console.log(`  ${source}: $${Number(amount).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    });
    console.log(`  Total Other Income: $${Number(revenueKpi.breakdown.other_income.total).toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    console.log("");
  }
  
  // Show revenue composition
  console.log("Revenue Composition:");
  console.log("--------------------");
  console.log(`Practice Income: ${revenueKpi.metrics.practice_vs_other.practice_percentage.toFixed(1)}%`);
  console.log(`Other Income: ${revenueKpi.metrics.practice_vs_other.other_percentage.toFixed(1)}%`);
  console.log("");
  
  // Calculate average revenue per doctor
  if (revenueKpi.summary.number_of_doctors > 0) {
    const avgRevenuePerDoctor = revenueKpi.summary.practice_income / revenueKpi.summary.number_of_doctors;
    console.log("Additional Metrics:");
    console.log("-------------------");
    console.log(`Average Revenue per Doctor: $${avgRevenuePerDoctor.toLocaleString('en-US', {minimumFractionDigits: 2})}`);
    
    // Show doctor performance relative to average
    if (revenueKpi.metrics.revenue_by_doctor) {
      console.log("\nDoctor Performance vs Average:");
      revenueKpi.metrics.revenue_by_doctor.forEach(doctor => {
        const vsAverage = ((doctor.amount - avgRevenuePerDoctor) / avgRevenuePerDoctor * 100);
        const indicator = vsAverage >= 0 ? '+' : '';
        console.log(`  Dr. ${doctor.doctor}: ${indicator}${vsAverage.toFixed(1)}% vs average`);
      });
    }
  }
  
  await pg.end();
}

queryRevenue().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
