#!/usr/bin/env node
/**
 * PostgreSQL Migration Runner
 * 
 * Runs all database migrations in the correct order and tracks execution.
 * This script creates all necessary tables for the QuickBooks data system.
 * 
 * Usage:
 *   node run_migrations.js
 *   node run_migrations.js --verify-only
 *   node run_migrations.js --rollback
 */

import "dotenv/config";
import { Client } from "pg";
import { readFileSync, readdirSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

// Migration files in order
const MIGRATIONS = [
  '001_create_accounts_table.sql',
  '002_create_pnl_reports_table.sql',
  '003_create_pnl_lines_table.sql',
  '004_create_pnl_summaries_table.sql',
  '005_create_pnl_ebitda_table.sql',
  '006_create_cashflow_reports_table.sql',
  '007_create_cashflow_lines_table.sql',
  '008_create_cashflow_totals_table.sql',
  '009_create_kpi_snapshots_table.sql',
  '010_create_ebitda_account_map_table.sql'
];

const EXPECTED_TABLES = [
  'accounts',
  'pnl_reports',
  'pnl_lines',
  'pnl_summaries',
  'pnl_ebitda',
  'cashflow_reports',
  'cashflow_lines',
  'cashflow_totals',
  'kpi_snapshots',
  'ebitda_account_map'
];

async function createMigrationTrackingTable() {
  console.log('📋 Setting up migration tracking...');
  
  await pg.query(`
    CREATE TABLE IF NOT EXISTS migration_history (
      id SERIAL PRIMARY KEY,
      migration_name VARCHAR(255) NOT NULL UNIQUE,
      executed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      success BOOLEAN NOT NULL DEFAULT TRUE,
      error_message TEXT
    )
  `);
  
  console.log('✅ Migration tracking table ready');
}

async function getMigrationHistory() {
  const { rows } = await pg.query(`
    SELECT migration_name, executed_at, success, error_message
    FROM migration_history 
    ORDER BY executed_at
  `);
  return rows;
}

async function logMigration(migrationName, success = true, errorMessage = null) {
  await pg.query(`
    INSERT INTO migration_history (migration_name, success, error_message) 
    VALUES ($1, $2, $3)
    ON CONFLICT (migration_name) 
    DO UPDATE SET 
      executed_at = NOW(),
      success = $2,
      error_message = $3
  `, [migrationName, success, errorMessage]);
}

async function runMigration(migrationFile) {
  const migrationPath = join(__dirname, 'migrations', migrationFile);
  const migrationName = migrationFile.replace('.sql', '');
  
  try {
    console.log(`🔄 Running ${migrationFile}...`);
    
    const sql = readFileSync(migrationPath, 'utf8');
    
    // Remove comments and split by semicolon for individual statements
    const statements = sql
      .split('\n')
      .filter(line => !line.trim().startsWith('--') && line.trim())
      .join('\n')
      .split(';')
      .filter(stmt => stmt.trim());
    
    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        await pg.query(statement.trim());
      }
    }
    
    await logMigration(migrationName, true);
    console.log(`✅ ${migrationFile} completed successfully`);
    
  } catch (error) {
    await logMigration(migrationName, false, error.message);
    console.error(`❌ ${migrationFile} failed:`, error.message);
    throw error;
  }
}

async function verifySchema() {
  console.log('\n🔍 Verifying database schema...');
  
  // Check if all expected tables exist
  const { rows: tables } = await pg.query(`
    SELECT table_name
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = ANY($1)
  `, [EXPECTED_TABLES]);
  
  const existingTables = tables.map(row => row.table_name);
  const missingTables = EXPECTED_TABLES.filter(table => !existingTables.includes(table));
  
  if (missingTables.length === 0) {
    console.log('✅ All expected tables exist');
  } else {
    console.log(`❌ Missing tables: ${missingTables.join(', ')}`);
    return false;
  }
  
  // Check foreign key constraints
  const { rows: constraints } = await pg.query(`
    SELECT COUNT(*) as constraint_count
    FROM information_schema.table_constraints 
    WHERE table_schema = 'public' 
    AND constraint_type = 'FOREIGN KEY'
  `);
  
  console.log(`📊 Foreign key constraints: ${constraints[0].constraint_count}`);
  
  // Check indexes
  const { rows: indexes } = await pg.query(`
    SELECT COUNT(*) as index_count
    FROM pg_indexes 
    WHERE schemaname = 'public'
    AND tablename = ANY($1)
  `, [EXPECTED_TABLES]);
  
  console.log(`📊 Indexes created: ${indexes[0].index_count}`);
  
  return true;
}

async function rollbackMigrations() {
  console.log('🔄 Rolling back migrations...');
  
  const dropOrder = [
    'ebitda_account_map',
    'kpi_snapshots', 
    'cashflow_totals',
    'cashflow_lines',
    'cashflow_reports',
    'pnl_ebitda',
    'pnl_summaries',
    'pnl_lines',
    'pnl_reports',
    'accounts',
    'migration_history'
  ];
  
  for (const table of dropOrder) {
    try {
      await pg.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
      console.log(`✅ Dropped table: ${table}`);
    } catch (error) {
      console.error(`❌ Failed to drop ${table}:`, error.message);
    }
  }
  
  console.log('✅ Rollback completed');
}

async function displaySummary() {
  console.log('\n📊 Migration Summary:');
  
  const history = await getMigrationHistory();
  
  if (history.length === 0) {
    console.log('No migrations found in history');
    return;
  }
  
  history.forEach(migration => {
    const status = migration.success ? '✅' : '❌';
    const time = migration.executed_at.toISOString().replace('T', ' ').substring(0, 19);
    console.log(`${status} ${migration.migration_name} (${time})`);
    
    if (!migration.success && migration.error_message) {
      console.log(`   Error: ${migration.error_message}`);
    }
  });
  
  const successCount = history.filter(m => m.success).length;
  console.log(`\n📈 Success rate: ${successCount}/${history.length} migrations`);
}

async function main() {
  const args = process.argv.slice(2);
  const verifyOnly = args.includes('--verify-only');
  const rollback = args.includes('--rollback');
  
  try {
    await pg.connect();
    console.log('🔌 Connected to PostgreSQL database');
    
    if (rollback) {
      await rollbackMigrations();
      return;
    }
    
    await createMigrationTrackingTable();
    
    if (verifyOnly) {
      const isValid = await verifySchema();
      process.exit(isValid ? 0 : 1);
    }
    
    console.log('\n🚀 Starting database migrations...');
    
    // Get existing migration history
    const history = await getMigrationHistory();
    const completedMigrations = new Set(
      history.filter(m => m.success).map(m => m.migration_name)
    );
    
    // Run migrations
    for (const migrationFile of MIGRATIONS) {
      const migrationName = migrationFile.replace('.sql', '');
      
      if (completedMigrations.has(migrationName)) {
        console.log(`⏭️  Skipping ${migrationFile} (already completed)`);
        continue;
      }
      
      await runMigration(migrationFile);
    }
    
    // Verify the schema
    console.log('\n🔍 Verifying schema...');
    const isValid = await verifySchema();
    
    if (isValid) {
      console.log('\n🎉 All migrations completed successfully!');
      console.log('You can now run your QuickBooks data ingestion scripts:');
      console.log('  node ingest_accounts.js');
      console.log('  node ingest_pnl.js');
      console.log('  node ingest_cashflow.js');
    } else {
      console.log('\n⚠️  Migrations completed but schema verification failed');
      process.exit(1);
    }
    
    await displaySummary();
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pg.end();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Migration interrupted');
  await pg.end();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\n⚠️  Migration terminated');
  await pg.end();
  process.exit(1);
});

main().catch(console.error);
