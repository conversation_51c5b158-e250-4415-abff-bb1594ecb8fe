#!/usr/bin/env node
/**
 * Seed default EBITDA account mappings for common account patterns.
 * This helps classify accounts into Interest, Taxes, Depreciation, and Amortization categories.
 * 
 * Usage:
 *   node seed_ebitda_mappings.js
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

// Default EBITDA account mappings
const defaultMappings = [
  // Interest Expense patterns
  {
    account_name_pattern: "%interest expense%",
    ebitda_category: "Interest",
    priority: 10,
  },
  {
    account_name_pattern: "%interest paid%",
    ebitda_category: "Interest",
    priority: 20,
  },
  {
    account_name_pattern: "%loan interest%",
    ebitda_category: "Interest",
    priority: 30,
  },
  {
    account_name_pattern: "%financing cost%",
    ebitda_category: "Interest",
    priority: 40,
  },
  {
    account_name_pattern: "%bank interest%",
    ebitda_category: "Interest",
    priority: 50,
  },
  
  // Tax Expense patterns
  {
    account_name_pattern: "%income tax%",
    ebitda_category: "Taxes",
    priority: 10,
  },
  {
    account_name_pattern: "%tax expense%",
    ebitda_category: "Taxes",
    priority: 20,
  },
  {
    account_name_pattern: "%federal tax%",
    ebitda_category: "Taxes",
    priority: 30,
  },
  {
    account_name_pattern: "%state tax%",
    ebitda_category: "Taxes",
    priority: 40,
  },
  {
    account_name_pattern: "%corporate tax%",
    ebitda_category: "Taxes",
    priority: 50,
  },
  
  // Depreciation patterns
  {
    account_name_pattern: "%depreciation%",
    ebitda_category: "Depreciation",
    priority: 10,
  },
  {
    account_name_pattern: "%deprec%",
    ebitda_category: "Depreciation",
    priority: 20,
  },
  {
    account_name_pattern: "%equipment depreciation%",
    ebitda_category: "Depreciation",
    priority: 5,
  },
  {
    account_name_pattern: "%building depreciation%",
    ebitda_category: "Depreciation",
    priority: 5,
  },
  
  // Amortization patterns
  {
    account_name_pattern: "%amortization%",
    ebitda_category: "Amortization",
    priority: 10,
  },
  {
    account_name_pattern: "%amort%",
    ebitda_category: "Amortization",
    priority: 20,
  },
  {
    account_name_pattern: "%goodwill amortization%",
    ebitda_category: "Amortization",
    priority: 5,
  },
  {
    account_name_pattern: "%intangible amortization%",
    ebitda_category: "Amortization",
    priority: 5,
  },
];

async function seedMappings() {
  await pg.connect();
  
  console.log("Seeding EBITDA account mappings...");
  
  for (const mapping of defaultMappings) {
    await pg.query(
      `
      INSERT INTO ebitda_account_map (account_name_pattern, ebitda_category, priority)
      VALUES ($1, $2, $3)
      ON CONFLICT DO NOTHING
      `,
      [mapping.account_name_pattern, mapping.ebitda_category, mapping.priority]
    );
  }
  
  console.log(`✅ Seeded ${defaultMappings.length} EBITDA account mappings.`);
  
  // Show current mappings
  const { rows } = await pg.query(`
    SELECT ebitda_category, account_name_pattern, priority 
    FROM ebitda_account_map 
    ORDER BY ebitda_category, priority
  `);
  
  console.log("\nCurrent EBITDA mappings:");
  let currentCategory = "";
  for (const row of rows) {
    if (row.ebitda_category !== currentCategory) {
      currentCategory = row.ebitda_category;
      console.log(`\n${currentCategory}:`);
    }
    console.log(`  ${row.account_name_pattern} (priority: ${row.priority})`);
  }
  
  await pg.end();
}

seedMappings().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
