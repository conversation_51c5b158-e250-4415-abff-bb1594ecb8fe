#!/usr/bin/env node
/**
 * Database Schema Verification Script
 * 
 * Verifies that all tables, indexes, and constraints are properly created
 * and provides detailed information about the database schema.
 * 
 * Usage:
 *   node verify_database.js
 *   node verify_database.js --detailed
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

const EXPECTED_TABLES = [
  'accounts',
  'pnl_reports',
  'pnl_lines',
  'pnl_summaries',
  'pnl_ebitda',
  'cashflow_reports',
  'cashflow_lines',
  'cashflow_totals',
  'kpi_snapshots',
  'ebitda_account_map'
];

async function checkTableExistence() {
  console.log('🔍 Checking table existence...');
  
  const { rows } = await pg.query(`
    SELECT table_name
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = ANY($1)
    ORDER BY table_name
  `, [EXPECTED_TABLES]);
  
  const existingTables = rows.map(row => row.table_name);
  const missingTables = EXPECTED_TABLES.filter(table => !existingTables.includes(table));
  
  if (missingTables.length === 0) {
    console.log(`✅ All ${EXPECTED_TABLES.length} expected tables exist`);
    return true;
  } else {
    console.log(`❌ Missing ${missingTables.length} tables:`);
    missingTables.forEach(table => console.log(`   - ${table}`));
    return false;
  }
}

async function checkTableDetails() {
  console.log('\n📊 Table details:');
  
  const { rows } = await pg.query(`
    SELECT 
      t.table_name,
      COUNT(c.column_name) as column_count,
      pg_size_pretty(pg_total_relation_size(t.table_name::regclass)) as size
    FROM information_schema.tables t
    LEFT JOIN information_schema.columns c 
      ON t.table_name = c.table_name 
      AND t.table_schema = c.table_schema
    WHERE t.table_schema = 'public' 
    AND t.table_name = ANY($1)
    GROUP BY t.table_name, pg_total_relation_size(t.table_name::regclass)
    ORDER BY t.table_name
  `, [EXPECTED_TABLES]);
  
  rows.forEach(row => {
    console.log(`  📋 ${row.table_name}: ${row.column_count} columns, ${row.size}`);
  });
}

async function checkForeignKeys() {
  console.log('\n🔗 Foreign key constraints:');
  
  const { rows } = await pg.query(`
    SELECT 
      tc.table_name,
      tc.constraint_name,
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name,
      rc.delete_rule,
      rc.update_rule
    FROM information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name = ANY($1)
    ORDER BY tc.table_name, tc.constraint_name
  `, [EXPECTED_TABLES]);
  
  if (rows.length === 0) {
    console.log('  ⚠️  No foreign key constraints found');
  } else {
    rows.forEach(row => {
      console.log(`  🔗 ${row.table_name}.${row.column_name} → ${row.foreign_table_name}.${row.foreign_column_name}`);
      console.log(`     Delete: ${row.delete_rule}, Update: ${row.update_rule}`);
    });
  }
}

async function checkIndexes() {
  console.log('\n📊 Index summary:');
  
  const { rows } = await pg.query(`
    SELECT 
      schemaname,
      tablename,
      COUNT(*) as index_count,
      array_agg(indexname ORDER BY indexname) as index_names
    FROM pg_indexes 
    WHERE schemaname = 'public'
    AND tablename = ANY($1)
    GROUP BY schemaname, tablename
    ORDER BY tablename
  `, [EXPECTED_TABLES]);
  
  rows.forEach(row => {
    console.log(`  📊 ${row.tablename}: ${row.index_count} indexes`);
    if (process.argv.includes('--detailed')) {
      row.index_names.forEach(indexName => {
        console.log(`     - ${indexName}`);
      });
    }
  });
}

async function checkConstraints() {
  console.log('\n✅ Check constraints:');
  
  const { rows } = await pg.query(`
    SELECT 
      tc.table_name,
      tc.constraint_name,
      cc.check_clause
    FROM information_schema.table_constraints tc
    JOIN information_schema.check_constraints cc 
      ON tc.constraint_name = cc.constraint_name
    WHERE tc.table_schema = 'public'
    AND tc.constraint_type = 'CHECK'
    AND tc.table_name = ANY($1)
    ORDER BY tc.table_name
  `, [EXPECTED_TABLES]);
  
  if (rows.length === 0) {
    console.log('  ℹ️  No check constraints found');
  } else {
    rows.forEach(row => {
      console.log(`  ✅ ${row.table_name}.${row.constraint_name}`);
      if (process.argv.includes('--detailed')) {
        console.log(`     ${row.check_clause}`);
      }
    });
  }
}

async function checkMigrationHistory() {
  console.log('\n📜 Migration history:');
  
  try {
    const { rows } = await pg.query(`
      SELECT 
        migration_name,
        executed_at,
        success,
        error_message
      FROM migration_history 
      ORDER BY executed_at
    `);
    
    if (rows.length === 0) {
      console.log('  ℹ️  No migration history found');
    } else {
      const successCount = rows.filter(r => r.success).length;
      console.log(`  📈 ${successCount}/${rows.length} migrations successful`);
      
      if (process.argv.includes('--detailed')) {
        rows.forEach(row => {
          const status = row.success ? '✅' : '❌';
          const time = row.executed_at.toISOString().replace('T', ' ').substring(0, 19);
          console.log(`    ${status} ${row.migration_name} (${time})`);
          if (!row.success && row.error_message) {
            console.log(`       Error: ${row.error_message}`);
          }
        });
      }
    }
  } catch (error) {
    console.log('  ⚠️  Migration history table not found');
  }
}

async function checkDataCounts() {
  console.log('\n📊 Data summary:');
  
  for (const table of EXPECTED_TABLES) {
    try {
      const { rows } = await pg.query(`SELECT COUNT(*) as count FROM ${table}`);
      const count = parseInt(rows[0].count);
      const icon = count > 0 ? '📊' : '📭';
      console.log(`  ${icon} ${table}: ${count.toLocaleString()} records`);
    } catch (error) {
      console.log(`  ❌ ${table}: Error reading table`);
    }
  }
}

async function performHealthCheck() {
  console.log('\n🏥 Database health check:');
  
  try {
    // Check database connection
    const { rows: connInfo } = await pg.query('SELECT version(), current_database(), current_user');
    console.log(`  ✅ PostgreSQL: ${connInfo[0].version.split(' ')[1]}`);
    console.log(`  ✅ Database: ${connInfo[0].current_database}`);
    console.log(`  ✅ User: ${connInfo[0].current_user}`);
    
    // Check database size
    const { rows: sizeInfo } = await pg.query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
    `);
    console.log(`  ✅ Database size: ${sizeInfo[0].db_size}`);
    
    return true;
  } catch (error) {
    console.log(`  ❌ Health check failed: ${error.message}`);
    return false;
  }
}

async function main() {
  try {
    await pg.connect();
    console.log('🔌 Connected to PostgreSQL database\n');
    
    console.log('=== Database Schema Verification ===');
    
    const tablesExist = await checkTableExistence();
    
    if (!tablesExist) {
      console.log('\n❌ Schema verification failed - missing tables');
      console.log('Run: node run_migrations.js');
      process.exit(1);
    }
    
    await checkTableDetails();
    await checkForeignKeys();
    await checkIndexes();
    await checkConstraints();
    await checkMigrationHistory();
    await checkDataCounts();
    
    const healthOk = await performHealthCheck();
    
    console.log('\n=== Verification Summary ===');
    
    if (tablesExist && healthOk) {
      console.log('🎉 Database schema verification passed!');
      console.log('Your database is ready for QuickBooks data ingestion.');
      process.exit(0);
    } else {
      console.log('⚠️  Some issues were found during verification.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  } finally {
    await pg.end();
  }
}

main().catch(console.error);
